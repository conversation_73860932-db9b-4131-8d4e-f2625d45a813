# CPA Quebec Parser с экспортом в Excel и пагинацией

## 🚀 Установка

1. **Установите зависимости:**
```bash
npm install
```

Или установите вручную:
```bash
npm install puppeteer-extra puppeteer-extra-plugin-stealth axios dotenv xlsx
```

2. **Создайте файл .env (опционально):**
```
TWOCAPTCHA_API_KEY=ваш_ключ_2captcha
ANTICAPTCHA_API_KEY=ваш_ключ_anticaptcha
```

## 📊 Использование

### Основные команды:

```bash
# Парсинг одной категории с сохранением в Excel (все страницы)
node quebec_parser_puppeteer.js --visible --category "Individuals"

# Парсинг нескольких категорий (по одной за раз, все страницы)
node quebec_parser_puppeteer.js --visible --categories "Individuals" "SMEs" "Start-ups"

# Парсинг всех категорий (все страницы)
node quebec_parser_puppeteer.js --visible --by-category

# Ограничение количества страниц
node quebec_parser_puppeteer.js --visible --category "SMEs" --max-pages 5

# Быстрый режим с ограничением страниц
node quebec_parser_puppeteer.js --visible --fast --category "Individuals" --max-pages 3
```

## 📁 Результаты

Парсер создает файлы в папке `./output/`:

### Excel файл (.xlsx) содержит:
- **Лист "CPA Results"** - основные данные:
  - № записи
  - Имя CPA
  - Email
  - Телефон
  - Ссылка на профиль
  - Категории
  - **Страница** (новое поле!)
  - Дополнительная информация
  - Дата парсинга

- **Лист "Статистика"** - общая статистика:
  - Общее количество записей
  - Записи с email
  - Записи с телефоном
  - Записи с профилем
  - Дата создания отчета

- **Лист "По категориям"** - статистика по категориям:
  - Название категории
  - Количество записей
  - Процент от общего

### JSON файл (.json) - резервная копия в JSON формате

## 🎯 Особенности

- ✅ **Только одна галочка за раз** - для более точных результатов
- ✅ **Автоматическая пагинация** - обрабатывает ВСЕ страницы результатов
- ✅ **Умное обнаружение пагинации** - находит кнопки "Next", "Suivant", ">"
- ✅ **Контроль количества страниц** - опция --max-pages
- ✅ **Автоматическое решение капчи** (при наличии API ключей)
- ✅ **Эмуляция человеческого поведения**
- ✅ **Экспорт в Excel с красивым форматированием**
- ✅ **Подробная статистика**
- ✅ **Автоматическая ширина колонок**
- ✅ **Номер страницы для каждого результата**

## 📋 Доступные категории

- Individuals
- Large companies
- NFPOs
- Professional firms
- Public corporations
- Retailers
- Self-employed workers
- SMEs
- Start-ups
- Syndicates of co-owners

## 🔧 Опции

- `--visible` - запуск в видимом режиме
- `--debug` - отладочный режим
- `--slow` - медленный режим (максимальные задержки)
- `--fast` - быстрый режим (минимальные задержки)
- `--category <название>` - парсинг одной категории
- `--categories <список>` - парсинг нескольких категорий
- `--by-category` - парсинг всех категорий
- `--max-pages <число>` - максимальное количество страниц (по умолчанию: 10)
- `--help` - справка

## 💡 Примеры использования

```bash
# Простой запуск (категория "Individuals", до 10 страниц)
node quebec_parser_puppeteer.js --visible

# Конкретная категория с ограничением страниц
node quebec_parser_puppeteer.js --visible --category "SMEs" --max-pages 5

# Несколько категорий с большим лимитом страниц
node quebec_parser_puppeteer.js --visible --categories "Individuals" "SMEs" --max-pages 20

# Все категории подряд (осторожно - может занять много времени!)
node quebec_parser_puppeteer.js --visible --by-category --max-pages 3

# Быстрый режим для тестирования
node quebec_parser_puppeteer.js --visible --fast --category "Individuals" --max-pages 2
```

## 📄 Пагинация

Парсер автоматически:
- 🔍 **Обнаруживает пагинацию** на странице результатов
- ➡️ **Переходит по страницам** с человеческими задержками
- 🛑 **Останавливается** при достижении последней страницы
- 📊 **Добавляет номер страницы** к каждому результату
- ⚡ **Контролирует лимит** через опцию --max-pages

Поддерживаемые элементы пагинации:
- Кнопки "Next", "Suivant" (французский)
- Ссылки с символами ">", "→"
- Стандартные классы `.pagination`, `.next`, `.page-next`
- Атрибуты `aria-label`, `rel="next"`

## 📈 Результат

После завершения парсинга вы получите:
- Excel файл с красиво отформатированными данными
- JSON файл для программной обработки
- Подробную статистику в консоли
- Информацию о количестве обработанных страниц

Пример вывода:
```
📊 === Завершено извлечение со всех страниц ===
📄 Обработано страниц: 5
📋 Всего результатов: 125
📊 Результаты сохранены в Excel: ./output/cpa_results_2024-01-15T10-30-45.xlsx
📈 Всего записей: 125
📧 С email: 118
📞 С телефоном: 95
🔗 С профилем: 125
``` 