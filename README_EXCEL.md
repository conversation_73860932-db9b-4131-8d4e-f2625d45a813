# CPA Quebec Parser с экспортом в Excel

## 🚀 Установка

1. **Установите зависимости:**
```bash
npm install
```

Или установите вручную:
```bash
npm install puppeteer-extra puppeteer-extra-plugin-stealth axios dotenv xlsx
```

2. **Создайте файл .env (опционально):**
```
TWOCAPTCHA_API_KEY=ваш_ключ_2captcha
ANTICAPTCHA_API_KEY=ваш_ключ_anticaptcha
```

## 📊 Использование

### Основные команды:

```bash
# Парсинг одной категории с сохранением в Excel
node quebec_parser_puppeteer.js --visible --category "Individuals"

# Парсинг нескольких категорий (по одной за раз)
node quebec_parser_puppeteer.js --visible --categories "Individuals" "SMEs" "Start-ups"

# Парсинг всех категорий
node quebec_parser_puppeteer.js --visible --by-category

# Быстрый режим
node quebec_parser_puppeteer.js --visible --fast --category "SMEs"
```

## 📁 Результаты

Парсер создает файлы в папке `./output/`:

### Excel файл (.xlsx) содержит:
- **Лист "CPA Results"** - основные данные:
  - № записи
  - Имя CPA
  - Email
  - Телефон
  - Ссылка на профиль
  - Категории
  - Дополнительная информация
  - Дата парсинга

- **Лист "Статистика"** - общая статистика:
  - Общее количество записей
  - Записи с email
  - Записи с телефоном
  - Записи с профилем
  - Дата создания отчета

- **Лист "По категориям"** - статистика по категориям:
  - Название категории
  - Количество записей
  - Процент от общего

### JSON файл (.json) - резервная копия в JSON формате

## 🎯 Особенности

- ✅ **Только одна галочка за раз** - для более точных результатов
- ✅ **Автоматическое решение капчи** (при наличии API ключей)
- ✅ **Эмуляция человеческого поведения**
- ✅ **Экспорт в Excel с красивым форматированием**
- ✅ **Подробная статистика**
- ✅ **Автоматическая ширина колонок**

## 📋 Доступные категории

- Individuals
- Large companies
- NFPOs
- Professional firms
- Public corporations
- Retailers
- Self-employed workers
- SMEs
- Start-ups
- Syndicates of co-owners

## 🔧 Опции

- `--visible` - запуск в видимом режиме
- `--debug` - отладочный режим
- `--slow` - медленный режим (максимальные задержки)
- `--fast` - быстрый режим (минимальные задержки)
- `--category <название>` - парсинг одной категории
- `--categories <список>` - парсинг нескольких категорий
- `--by-category` - парсинг всех категорий
- `--help` - справка

## 💡 Примеры использования

```bash
# Простой запуск (категория "Individuals")
node quebec_parser_puppeteer.js --visible

# Конкретная категория
node quebec_parser_puppeteer.js --visible --category "SMEs"

# Несколько категорий
node quebec_parser_puppeteer.js --visible --categories "Individuals" "SMEs" "Start-ups"

# Все категории подряд
node quebec_parser_puppeteer.js --visible --by-category

# Быстрый режим для тестирования
node quebec_parser_puppeteer.js --visible --fast --category "Individuals"
```

## 📈 Результат

После завершения парсинга вы получите:
- Excel файл с красиво отформатированными данными
- JSON файл для программной обработки
- Подробную статистику в консоли

Пример вывода:
```
📊 Результаты сохранены в Excel: ./output/cpa_results_2024-01-15T10-30-45.xlsx
📈 Всего записей: 25
📧 С email: 23
📞 С телефоном: 20
�� С профилем: 25
``` 