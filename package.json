{"name": "quebec-cpa-parser", "version": "1.0.0", "description": "CPA Quebec Parser с эмуляцией человеческого поведения и экспортом в Excel", "main": "quebec_parser_puppeteer.js", "scripts": {"start": "node quebec_parser_puppeteer.js", "install-deps": "npm install puppeteer-extra puppeteer-extra-plugin-stealth axios dotenv xlsx"}, "keywords": ["parser", "puppeteer", "cpa", "quebec", "excel", "automation"], "author": "AI Assistant", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "xlsx": "^0.18.5"}, "devDependencies": {"puppeteer": "^21.0.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/cpa-quebec-parser"}, "bugs": {"url": "https://github.com/your-repo/cpa-quebec-parser/issues"}, "homepage": "https://github.com/your-repo/cpa-quebec-parser#readme"}