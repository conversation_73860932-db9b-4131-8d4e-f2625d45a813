#!/usr/bin/env node

/**
 * CPA Quebec Parser с эмуляцией человеческого поведения (Puppeteer версия)
 * Основан на реальном анализе структуры сайта
 * С автоматическим решением капчи через Anti-Captcha API
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const XLSX = require('xlsx'); // Добавляем библиотеку для работы с Excel
const { saveResultsToExcel } = require('./excel_functions'); // Импортируем функцию Excel
require('dotenv').config();

// Подключаем stealth plugin
puppeteer.use(StealthPlugin());

// Константы
const BASE_URL = 'https://cpaquebec.ca/en/find-a-cpa/cpa-directory/';
const OUTPUT_DIR = './output';
const ANTICAPTCHA_KEY = process.env.ANTICAPTCHA_API_KEY;
const TWOCAPTCHA_KEY = process.env.TWOCAPTCHA_API_KEY;

// Глобальный множитель задержек
let DELAY_MULTIPLIER = 1.0;

// Категории клиентов
const CLIENT_CATEGORIES = {
    "Individuals": "ListeClienteleDesserviesLeftColumn_0__Selected",
    "Large companies": "ListeClienteleDesserviesLeftColumn_1__Selected",
    "NFPOs": "ListeClienteleDesserviesLeftColumn_2__Selected",
    "Professional firms": "ListeClienteleDesserviesLeftColumn_3__Selected",
    "Public corporations": "ListeClienteleDesserviesLeftColumn_4__Selected",
    "Retailers": "ListeClienteleDesserviesRightColumn_0__Selected",
    "Self-employed workers": "ListeClienteleDesserviesRightColumn_1__Selected",
    "SMEs": "ListeClienteleDesserviesRightColumn_2__Selected",
    "Start-ups": "ListeClienteleDesserviesRightColumn_3__Selected",
    "Syndicates of co-owners": "ListeClienteleDesserviesRightColumn_4__Selected"
};

// === ФУНКЦИИ ЭМУЛЯЦИИ ЧЕЛОВЕЧЕСКОГО ПОВЕДЕНИЯ ===

async function humanDelay(minMs = 500, maxMs = 2000, reason = '') {
    const delay = (Math.random() * (maxMs - minMs) + minMs) * DELAY_MULTIPLIER;
    if (reason) {
        console.log(`🕐 Человеческая пауза ${(delay/1000).toFixed(2)}с (${reason})`);
    } else {
        console.log(`🕐 Человеческая пауза ${(delay/1000).toFixed(2)}с`);
    }
    await new Promise(resolve => setTimeout(resolve, delay));
}

async function humanMouseMovement(page, targetSelector = null) {
    try {
        const viewport = page.viewport();
        const width = viewport.width || 1280;
        const height = viewport.height || 800;

        // Генерируем случайные начальные координаты
        const startX = Math.random() * (width - 200) + 100;
        const startY = Math.random() * (height - 200) + 100;

        let endX, endY;

        if (targetSelector) {
            try {
                const element = await page.$(targetSelector);
                if (element) {
                    const box = await element.boundingBox();
                    if (box) {
                        endX = box.x + box.width / 2;
                        endY = box.y + box.height / 2;
                    } else {
                        endX = Math.random() * (width - 200) + 100;
                        endY = Math.random() * (height - 200) + 100;
                    }
                } else {
                    endX = Math.random() * (width - 200) + 100;
                    endY = Math.random() * (height - 200) + 100;
                }
            } catch (e) {
                endX = Math.random() * (width - 200) + 100;
                endY = Math.random() * (height - 200) + 100;
            }
        } else {
            endX = Math.random() * (width - 200) + 100;
            endY = Math.random() * (height - 200) + 100;
        }

        // Создаем плавную траекторию
        const steps = Math.floor(Math.random() * 10) + 5;
        for (let i = 0; i < steps; i++) {
            const progress = i / (steps - 1);
            const curveOffset = Math.sin(progress * Math.PI) * (Math.random() * 40 - 20);

            const currentX = startX + (endX - startX) * progress + curveOffset;
            const currentY = startY + (endY - startY) * progress + curveOffset;

            await page.mouse.move(currentX, currentY);
            await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10));
        }

        console.log(`🖱️ Движение мыши: (${startX.toFixed(0)}, ${startY.toFixed(0)}) -> (${endX.toFixed(0)}, ${endY.toFixed(0)})`);

    } catch (error) {
        console.log(`❌ Ошибка при движении мыши: ${error.message}`);
    }
}

async function humanScroll(page, direction = 'down', amount = null) {
    try {
        if (!amount) {
            amount = Math.random() * 600 + 200;
        }

        const deltaY = direction === 'down' ? amount : -amount;
        const steps = Math.floor(Math.random() * 5) + 3;
        const stepSize = deltaY / steps;

        for (let i = 0; i < steps; i++) {
            await page.mouse.wheel({ deltaY: stepSize });
            await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
        }

        console.log(`📜 Прокрутка ${direction} на ${amount.toFixed(0)}px`);

    } catch (error) {
        console.log(`❌ Ошибка при прокрутке: ${error.message}`);
    }
}

async function humanClick(page, selector, delayBefore = true, delayAfter = true) {
    try {
        if (delayBefore) {
            await humanDelay(300, 1500, 'перед кликом');
        }

        // Движение мыши к элементу
        await humanMouseMovement(page, selector);
        await humanDelay(100, 500, 'наведение на элемент');

        // Клик
        const element = await page.$(selector);
        if (element) {
            await element.click();
            console.log(`👆 Человеческий клик по: ${selector}`);
        } else {
            console.log(`⚠️ Элемент не найден для клика: ${selector}`);
            return false;
        }

        if (delayAfter) {
            await humanDelay(200, 1000, 'после клика');
        }

        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом клике по ${selector}: ${error.message}`);
        return false;
    }
}

async function humanType(page, selector, text, clearFirst = true) {
    try {
        const element = await page.$(selector);
        if (!element) {
            console.log(`⚠️ Элемент не найден для ввода: ${selector}`);
            return false;
        }

        // Фокусируемся на элементе
        await element.focus();
        await humanDelay(200, 500, 'фокус на поле');

        if (clearFirst) {
            await element.click({ clickCount: 3 }); // Выделяем весь текст
            await humanDelay(100, 300, 'очистка поля');
        }

        // Вводим текст посимвольно
        for (const char of text) {
            await element.type(char);

            // Варьируем скорость ввода
            let delay;
            if (char === ' ') {
                delay = Math.random() * 200 + 100; // Пробелы быстрее
            } else if (char === char.toUpperCase() && char !== char.toLowerCase()) {
                delay = Math.random() * 250 + 150; // Заглавные медленнее
            } else {
                delay = Math.random() * 200 + 50; // Обычные символы
            }

            await new Promise(resolve => setTimeout(resolve, delay));
        }

        console.log(`⌨️ Человеческий ввод в ${selector}: ${text}`);
        await humanDelay(200, 600, 'после ввода');

        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом вводе в ${selector}: ${error.message}`);
        return false;
    }
}

async function simulateReadingPage(page, minTime = 3000, maxTime = 8000) {
    try {
        const readingTime = Math.random() * (maxTime - minTime) + minTime;
        console.log(`📖 Симуляция чтения страницы ${(readingTime/1000).toFixed(1)}с`);

        const startTime = Date.now();

        while (Date.now() - startTime < readingTime) {
            const action = ['scroll', 'mouse_move', 'pause'][Math.floor(Math.random() * 3)];

            if (action === 'scroll') {
                const direction = Math.random() > 0.25 ? 'down' : 'up'; // Больше вероятность вниз
                await humanScroll(page, direction, Math.random() * 300 + 100);
            } else if (action === 'mouse_move') {
                await humanMouseMovement(page);
            } else {
                await humanDelay(500, 2000, 'пауза при чтении');
            }
        }

    } catch (error) {
        console.log(`❌ Ошибка при симуляции чтения: ${error.message}`);
    }
}

// === ФУНКЦИИ 2CAPTCHA ===

async function create2CaptchaTask(siteKey, pageUrl) {
    if (!TWOCAPTCHA_KEY) {
        console.error('❌ TWOCAPTCHA_API_KEY не установлен');
        return { success: false, taskId: null };
    }

    console.log(`🔐 Создание задачи 2captcha, sitekey=${siteKey.substring(0, 8)}...`);

    const params = new URLSearchParams({
        key: TWOCAPTCHA_KEY,
        method: 'userrecaptcha',
        googlekey: siteKey,
        pageurl: pageUrl,
        json: 1
    });

    try {
        const response = await axios.post('http://2captcha.com/in.php', params, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        const data = response.data;
        console.log('📝 2captcha ответ:', data);

        if (data.status !== 1 || !data.request) {
            console.error(`❌ Ошибка 2captcha: ${data.error_text || 'Unknown error'}`);
            return { success: false, taskId: null };
        }

        console.log(`✅ Задача 2captcha создана, ID: ${data.request}`);
        return { success: true, taskId: data.request };

    } catch (error) {
        console.error(`❌ Ошибка при создании задачи 2captcha: ${error.message}`);
        return { success: false, taskId: null };
    }
}

// === ФУНКЦИИ ANTI-CAPTCHA (FALLBACK) ===

async function createCaptchaTask(siteKey, pageUrl) {
    if (!ANTICAPTCHA_KEY) {
        console.error('❌ ANTICAPTCHA_API_KEY не установлен');
        return { success: false, taskId: null };
    }

    console.log(`🔐 Создание задачи Anti-Captcha, sitekey=${siteKey.substring(0, 8)}...`);

    const taskPayload = {
        clientKey: ANTICAPTCHA_KEY,
        task: {
            type: 'NoCaptchaTaskProxyless',
            websiteURL: pageUrl,
            websiteKey: siteKey,
        },
        softId: 8041
    };

    try {
        const response = await axios.post('https://api.anti-captcha.com/createTask', taskPayload, {
            timeout: 30000
        });

        const data = response.data;
        console.log('📝 Anti-Captcha ответ:', data);

        if (data.errorId > 0 || !data.taskId) {
            console.error(`❌ Ошибка Anti-Captcha: ${data.errorCode} - ${data.errorDescription}`);
            return { success: false, taskId: null };
        }

        console.log(`✅ Задача Anti-Captcha создана, ID: ${data.taskId}`);
        return { success: true, taskId: data.taskId };

    } catch (error) {
        console.error(`❌ Ошибка при создании задачи Anti-Captcha: ${error.message}`);
        return { success: false, taskId: null };
    }
}

async function get2CaptchaResult(taskId, maxAttempts = 60) {
    if (!TWOCAPTCHA_KEY) {
        return { success: false, token: null };
    }

    console.log(`⏳ Ожидание результата 2captcha для задачи ${taskId}...`);

    await new Promise(resolve => setTimeout(resolve, 10000)); // Начальная задержка для 2captcha

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        try {
            const params = new URLSearchParams({
                key: TWOCAPTCHA_KEY,
                action: 'get',
                id: taskId,
                json: 1
            });

            const response = await axios.get(`http://2captcha.com/res.php?${params}`, {
                timeout: 30000
            });

            const data = response.data;

            if (data.status === 1 && data.request) {
                const token = data.request;
                if (token && token.length > 50) {
                    console.log(`✅ Капча решена 2captcha! Длина токена: ${token.length}`);
                    return { success: true, token: token };
                } else {
                    console.error('❌ 2captcha вернул пустой токен');
                    return { success: false, token: null };
                }
            } else if (data.request === 'CAPCHA_NOT_READY') {
                console.log(`⏳ Задача ${taskId} обрабатывается 2captcha... (попытка ${attempt + 1})`);
            } else if (data.error_text) {
                console.error(`❌ Ошибка 2captcha: ${data.error_text}`);
                return { success: false, token: null };
            } else {
                console.log(`⚠️ Неизвестный ответ 2captcha:`, data);
            }

            await new Promise(resolve => setTimeout(resolve, 5000)); // 2captcha рекомендует 5 сек

        } catch (error) {
            console.error(`❌ Ошибка при получении результата 2captcha: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }

    console.error(`❌ Превышено время ожидания для задачи 2captcha ${taskId}`);
    return { success: false, token: null };
}

async function getCaptchaResult(taskId, maxAttempts = 60) {
    if (!ANTICAPTCHA_KEY) {
        return { success: false, token: null };
    }

    console.log(`⏳ Ожидание результата Anti-Captcha для задачи ${taskId}...`);

    await new Promise(resolve => setTimeout(resolve, 5000)); // Начальная задержка

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        try {
            const response = await axios.post('https://api.anti-captcha.com/getTaskResult', {
                clientKey: ANTICAPTCHA_KEY,
                taskId: taskId
            }, { timeout: 30000 });

            const data = response.data;

            if (data.errorId > 0) {
                console.error(`❌ Ошибка Anti-Captcha: ${data.errorCode} - ${data.errorDescription}`);
                return { success: false, token: null };
            }

            if (data.status === 'ready') {
                const token = data.solution?.gRecaptchaResponse;
                if (token && token.length > 50) {
                    console.log(`✅ Капча решена! Длина токена: ${token.length}`);
                    return { success: true, token: token };
                } else {
                    console.error('❌ Anti-Captcha вернул пустой токен');
                    return { success: false, token: null };
                }
            } else if (data.status === 'processing') {
                console.log(`⏳ Задача ${taskId} обрабатывается... (попытка ${attempt + 1})`);
            } else {
                console.log(`⚠️ Неизвестный статус: ${data.status}`);
            }

            await new Promise(resolve => setTimeout(resolve, 3000));

        } catch (error) {
            console.error(`❌ Ошибка при получении результата: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.error(`❌ Превышено время ожидания для задачи ${taskId}`);
    return { success: false, token: null };
}

// === ФУНКЦИИ РАБОТЫ С КАПЧЕЙ ===

async function extractRecaptchaSitekey(page) {
    try {
        const sitekey = await page.evaluate(() => {
            // Ищем в div.g-recaptcha
            const recaptchaDiv = document.querySelector('.g-recaptcha');
            if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                return recaptchaDiv.getAttribute('data-sitekey');
            }

            // Ищем в iframe
            const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
            for (const iframe of iframes) {
                const src = iframe.src;
                const match = src.match(/k=([^&]+)/);
                if (match) {
                    return match[1];
                }
            }

            // Ищем в любом элементе с data-sitekey
            const element = document.querySelector('[data-sitekey]');
            if (element) {
                return element.getAttribute('data-sitekey');
            }

            return null;
        });

        if (sitekey) {
            console.log(`🔑 Найден sitekey: ${sitekey.substring(0, 10)}...`);
            return { success: true, sitekey };
        } else {
            console.error('❌ Sitekey не найден');
            return { success: false, sitekey: null };
        }
    } catch (error) {
        console.error(`❌ Ошибка при извлечении sitekey: ${error.message}`);
        return { success: false, sitekey: null };
    }
}

async function insertCaptchaToken(page, token) {
    try {
        await page.evaluate((token) => {
            // Находим или создаем поле g-recaptcha-response
            let response = document.querySelector('#g-recaptcha-response');
            if (!response) {
                response = document.createElement('textarea');
                response.id = 'g-recaptcha-response';
                response.name = 'g-recaptcha-response';
                response.className = 'g-recaptcha-response';
                response.style.display = 'none';
                document.body.appendChild(response);
            }

            // Вставляем токен
            response.value = token;
            response.dispatchEvent(new Event('change', { bubbles: true }));

            // Вызываем callback если есть
            if (window.grecaptcha && window.___grecaptcha_cfg) {
                const widgets = window.___grecaptcha_cfg.clients;
                for (const clientId in widgets) {
                    const client = widgets[clientId];
                    if (client.callback) {
                        client.callback(token);
                    }
                }
            }

            console.log('Токен капчи вставлен:', token.substring(0, 20) + '...');
        }, token);

        console.log('✅ Токен капчи успешно вставлен');
        return true;
    } catch (error) {
        console.error(`❌ Ошибка при вставке токена: ${error.message}`);
        return false;
    }
}

async function clickRecaptchaCheckboxHuman(page) {
    try {
        console.log('🔍 Ищем чекбокс reCAPTCHA...');

        // Расширенный поиск iframe с reCAPTCHA
        const recaptchaSelectors = [
            'iframe[src*="api2/anchor"]',
            'iframe[src*="recaptcha"]',
            'iframe[title*="reCAPTCHA"]',
            'iframe[title="reCAPTCHA"]',
            '.g-recaptcha iframe',
            'iframe[name*="recaptcha"]'
        ];

        let recaptchaFrame = null;
        let usedSelector = null;

        for (const selector of recaptchaSelectors) {
            try {
                recaptchaFrame = await page.$(selector);
                if (recaptchaFrame) {
                    usedSelector = selector;
                    console.log(`🎯 Найден iframe reCAPTCHA: ${selector}`);
                    break;
                }
            } catch (e) {
                // Продолжаем поиск
            }
        }

        if (!recaptchaFrame) {
            console.log('⚠️ reCAPTCHA iframe не найден, пробуем альтернативные методы...');

            // Альтернативный поиск через JavaScript
            const frameInfo = await page.evaluate(() => {
                const allIframes = document.querySelectorAll('iframe');
                const recaptchaIframes = [];

                allIframes.forEach((iframe, index) => {
                    const src = iframe.src || '';
                    const title = iframe.title || '';
                    const className = iframe.className || '';

                    if (src.includes('recaptcha') ||
                        title.toLowerCase().includes('recaptcha') ||
                        className.includes('recaptcha')) {
                        recaptchaIframes.push({
                            index,
                            src,
                            title,
                            className,
                            id: iframe.id
                        });
                    }
                });

                return {
                    totalIframes: allIframes.length,
                    recaptchaIframes
                };
            });

            console.log('📊 Информация об iframe:', frameInfo);

            if (frameInfo.recaptchaIframes.length > 0) {
                // Пробуем первый найденный iframe
                const firstRecaptcha = frameInfo.recaptchaIframes[0];
                if (firstRecaptcha.id) {
                    recaptchaFrame = await page.$(`#${firstRecaptcha.id}`);
                    usedSelector = `#${firstRecaptcha.id}`;
                } else {
                    // Пробуем по индексу
                    recaptchaFrame = await page.$(`iframe:nth-child(${firstRecaptcha.index + 1})`);
                    usedSelector = `iframe:nth-child(${firstRecaptcha.index + 1})`;
                }

                if (recaptchaFrame) {
                    console.log(`🎯 Найден iframe через JavaScript: ${usedSelector}`);
                }
            }
        }

        if (!recaptchaFrame) {
            console.log('❌ reCAPTCHA iframe не найден');
            return false;
        }

        // Симулируем движение мыши к области капчи
        await humanMouseMovement(page, usedSelector);
        await humanDelay(500, 1500, 'изучение капчи');

        // Получаем frame
        const frame = await recaptchaFrame.contentFrame();
        if (!frame) {
            console.log('⚠️ Не удалось получить frame reCAPTCHA');
            return false;
        }

        // Расширенный поиск чекбокса в frame
        const checkboxSelectors = [
            '#recaptcha-anchor',
            '.recaptcha-checkbox',
            'span.recaptcha-checkbox',
            'div.recaptcha-checkbox-border',
            '[role="checkbox"]',
            'input[type="checkbox"]'
        ];

        let checkbox = null;
        let checkboxSelector = null;

        for (const selector of checkboxSelectors) {
            try {
                checkbox = await frame.$(selector);
                if (checkbox) {
                    checkboxSelector = selector;
                    console.log(`🎯 Найден чекбокс reCAPTCHA: ${selector}`);
                    break;
                }
            } catch (e) {
                // Продолжаем поиск
            }
        }

        if (!checkbox) {
            console.log('⚠️ Чекбокс reCAPTCHA не найден, пробуем JavaScript клик...');

            // Пробуем кликнуть через JavaScript
            const jsClickResult = await frame.evaluate(() => {
                // Ищем элементы для клика
                const selectors = [
                    '#recaptcha-anchor',
                    '.recaptcha-checkbox',
                    'span.recaptcha-checkbox',
                    'div.recaptcha-checkbox-border',
                    '[role="checkbox"]'
                ];

                for (const sel of selectors) {
                    const element = document.querySelector(sel);
                    if (element) {
                        element.click();
                        console.log(`Клик через JS: ${sel}`);
                        return { success: true, selector: sel };
                    }
                }

                // Если ничего не найдено, кликаем по центру frame
                const rect = document.body.getBoundingClientRect();
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: centerX,
                    clientY: centerY
                });

                document.body.dispatchEvent(clickEvent);
                console.log('Клик по центру frame');
                return { success: true, selector: 'center' };
            });

            if (jsClickResult.success) {
                console.log(`✅ JavaScript клик выполнен: ${jsClickResult.selector}`);
                await humanDelay(1000, 2500, 'ожидание после клика по капче');
                return true;
            } else {
                console.log('❌ Не удалось кликнуть по чекбоксу');
                return false;
            }
        }

        // Человеческая задержка перед кликом
        await humanDelay(800, 2000, 'размышление перед кликом по капче');

        // Кликаем по чекбоксу
        try {
            await checkbox.click();
            console.log('✅ Человеческий клик по галочке "я не робот" выполнен');
        } catch (clickError) {
            console.log(`⚠️ Обычный клик не сработал: ${clickError.message}, пробуем JavaScript`);
            await frame.evaluate(el => el.click(), checkbox);
            console.log('✅ JavaScript клик по галочке "я не робот" выполнен');
        }

        // Человеческая задержка после клика
        await humanDelay(1000, 2500, 'ожидание после клика по капче');
        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом клике по галочке reCAPTCHA: ${error.message}`);
        return false;
    }
}

async function isRecaptchaSolved(page) {
    try {
        const hasToken = await page.evaluate(() => {
            const response = document.querySelector('#g-recaptcha-response');
            return response && response.value && response.value.length > 50;
        });

        if (hasToken) {
            return true;
        }

        // Проверяем состояние чекбокса
        const recaptchaFrame = await page.$('iframe[src*="api2/anchor"]');
        if (recaptchaFrame) {
            const frame = await recaptchaFrame.contentFrame();
            if (frame) {
                const checkbox = await frame.$('#recaptcha-anchor');
                if (checkbox) {
                    const isChecked = await frame.evaluate(el => el.getAttribute('aria-checked'), checkbox);
                    return isChecked === 'true';
                }
            }
        }

        return false;

    } catch (error) {
        console.error(`❌ Ошибка при проверке состояния reCAPTCHA: ${error.message}`);
        return false;
    }
}

async function simulateWaitingBehavior(page) {
    try {
        // Случайные действия во время ожидания
        const actions = ['mouse_move', 'scroll', 'pause', 'mouse_move'];

        for (let i = 0; i < Math.floor(Math.random() * 5) + 3; i++) {
            const action = actions[Math.floor(Math.random() * actions.length)];

            if (action === 'mouse_move') {
                await humanMouseMovement(page);
            } else if (action === 'scroll') {
                const direction = Math.random() > 0.5 ? 'down' : 'up';
                await humanScroll(page, direction, Math.random() * 150 + 50);
            } else {
                await humanDelay(1000, 3000, 'пауза во время ожидания');
            }

            await humanDelay(500, 1500, 'между действиями ожидания');
        }

    } catch (error) {
        console.log(`❌ Ошибка при симуляции ожидания: ${error.message}`);
    }
}

async function closeCaptchaWindow(page) {
    try {
        console.log('🔒 Принудительно закрываем окно капчи...');

        // Метод 1: Закрываем iframe с визуальной капчей
        const result1 = await page.evaluate(() => {
            const iframes = document.querySelectorAll('iframe[src*="api2/bframe"], iframe[src*="recaptcha"]');
            let closed = false;

            iframes.forEach(iframe => {
                try {
                    // Скрываем iframe
                    iframe.style.display = 'none';
                    iframe.style.visibility = 'hidden';
                    iframe.style.opacity = '0';
                    iframe.style.zIndex = '-9999';

                    // Удаляем iframe из DOM
                    if (iframe.parentNode) {
                        iframe.parentNode.removeChild(iframe);
                        closed = true;
                    }
                } catch (e) {
                    console.log('Ошибка при удалении iframe:', e);
                }
            });

            return closed;
        });

        if (result1) {
            console.log('✅ Iframe капчи удален');
        }

        // Метод 2: Закрываем overlay элементы
        const result2 = await page.evaluate(() => {
            const overlays = document.querySelectorAll('[style*="z-index"], .recaptcha-overlay, .captcha-overlay, div[style*="position: fixed"]');
            let closed = false;

            overlays.forEach(overlay => {
                try {
                    const style = window.getComputedStyle(overlay);
                    const zIndex = parseInt(style.zIndex) || 0;

                    // Если это высокий z-index (вероятно overlay)
                    if (zIndex > 1000) {
                        overlay.style.display = 'none';
                        overlay.style.visibility = 'hidden';
                        overlay.style.opacity = '0';
                        overlay.style.zIndex = '-9999';

                        // Пробуем удалить
                        if (overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                            closed = true;
                        }
                    }
                } catch (e) {
                    console.log('Ошибка при удалении overlay:', e);
                }
            });

            return closed;
        });

        if (result2) {
            console.log('✅ Overlay элементы удалены');
        }

        // Метод 3: Нажимаем Escape для закрытия модальных окон
        try {
            await page.keyboard.press('Escape');
            console.log('✅ Нажата клавиша Escape');
        } catch (e) {
            console.log('⚠️ Не удалось нажать Escape:', e.message);
        }

        // Метод 4: Ищем и кликаем кнопки закрытия
        const result4 = await page.evaluate(() => {
            const closeButtons = document.querySelectorAll('[aria-label*="close"], [title*="close"], .close, .modal-close, button[aria-label="Close"]');
            let clicked = false;

            closeButtons.forEach(button => {
                try {
                    if (button.offsetHeight > 0) { // Видимая кнопка
                        button.click();
                        clicked = true;
                    }
                } catch (e) {
                    console.log('Ошибка при клике на кнопку закрытия:', e);
                }
            });

            return clicked;
        });

        if (result4) {
            console.log('✅ Кнопки закрытия нажаты');
        }

        // Небольшая пауза после закрытия
        await humanDelay(500, 1000, 'после закрытия окна капчи');

        // Проверяем, закрылось ли окно
        const stillVisible = await page.evaluate(() => {
            const iframes = document.querySelectorAll('iframe[src*="api2/bframe"], iframe[src*="recaptcha"]');
            const visibleIframes = Array.from(iframes).filter(iframe => {
                const style = window.getComputedStyle(iframe);
                return style.display !== 'none' && style.visibility !== 'hidden' && iframe.offsetHeight > 0;
            });

            const overlays = document.querySelectorAll('[style*="z-index"]');
            const visibleOverlays = Array.from(overlays).filter(overlay => {
                const style = window.getComputedStyle(overlay);
                const zIndex = parseInt(style.zIndex) || 0;
                return zIndex > 1000 && style.display !== 'none' && style.visibility !== 'hidden';
            });

            return visibleIframes.length > 0 || visibleOverlays.length > 0;
        });

        if (!stillVisible) {
            console.log('✅ Окно капчи успешно закрыто');
            return true;
        } else {
            console.log('⚠️ Окно капчи все еще видимо после попыток закрытия');
            return false;
        }

    } catch (error) {
        console.log(`❌ Ошибка при закрытии окна капчи: ${error.message}`);
        return false;
    }
}

async function waitForCaptchaWindowToClose(page) {
    try {
        // Сначала пробуем принудительно закрыть
        const closed = await closeCaptchaWindow(page);
        if (closed) {
            return true;
        }

        console.log('🔍 Проверяем наличие окна визуальной капчи...');

        // Ищем iframe с визуальной капчей (challenge)
        const challengeSelectors = [
            'iframe[src*="api2/bframe"]',
            'iframe[src*="recaptcha/api2/bframe"]',
            'iframe[title*="recaptcha challenge"]',
            'iframe[title="recaptcha challenge expires in two minutes"]'
        ];

        // Проверяем наличие окна капчи
        let challengeFound = false;
        for (const selector of challengeSelectors) {
            try {
                const challengeFrame = await page.$(selector);
                if (challengeFrame) {
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);
                        return rect.width > 0 && rect.height > 0 &&
                               style.visibility !== 'hidden' &&
                               style.display !== 'none' &&
                               style.opacity !== '0';
                    }, challengeFrame);

                    if (isVisible) {
                        challengeFound = true;
                        console.log(`📋 Найдено окно визуальной капчи: ${selector}`);
                        break;
                    }
                }
            } catch (e) {
                // Продолжаем поиск
            }
        }

        if (!challengeFound) {
            console.log('✅ Окно визуальной капчи не найдено');
            return true;
        }

        // Ждем исчезновения окна капчи (уменьшили количество попыток)
        console.log('⏳ Ожидаем закрытия окна визуальной капчи...');

        for (let attempt = 0; attempt < 15; attempt++) {
            let stillVisible = false;

            for (const selector of challengeSelectors) {
                try {
                    const challengeFrame = await page.$(selector);
                    if (challengeFrame) {
                        const isVisible = await page.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none' &&
                                   style.opacity !== '0';
                        }, challengeFrame);

                        if (isVisible) {
                            stillVisible = true;
                            break;
                        }
                    }
                } catch (e) {
                    // Элемент исчез - это хорошо
                }
            }

            if (!stillVisible) {
                console.log('✅ Окно визуальной капчи закрылось!');
                await humanDelay(1000, 2000, 'стабилизация после закрытия капчи');
                return true;
            }

            console.log(`⏳ Окно капчи все еще видимо, ждем... (попытка ${attempt + 1}/15)`);
            await humanDelay(1000, 2000, 'ожидание закрытия окна капчи');
        }

        console.log('⚠️ Окно капчи не закрылось за отведенное время');
        return false;

    } catch (error) {
        console.log(`❌ Ошибка при ожидании закрытия окна капчи: ${error.message}`);
        return false;
    }
}

async function clickSearchButtonHuman(page) {
    try {
        console.log('🔍 Подготовка к нажатию кнопки поиска...');

        // Проверяем, что окно капчи точно исчезло
        console.log('🔍 Финальная проверка отсутствия окна капчи...');
        const captchaWindowClosed = await waitForCaptchaWindowToClose(page);
        if (!captchaWindowClosed) {
            console.log('⚠️ Окно капчи все еще видимо, но продолжаем...');
        }

        // ВАЖНО: После закрытия окна капчи нужно еще раз кликнуть на галочку "я не робот"
        console.log('✅ Повторный клик на галочку "я не робот" после закрытия окна капчи...');
        const finalCaptchaClick = await clickRecaptchaCheckboxHuman(page);
        if (finalCaptchaClick) {
            console.log('✅ Финальный клик по галочке выполнен успешно');
        } else {
            console.log('⚠️ Не удалось выполнить финальный клик по галочке, но продолжаем...');
        }

        // Симулируем финальную проверку формы
        await simulateReadingPage(page, 1000, 2500);

        // Ищем кнопку поиска с более точными селекторами
        const searchSelectors = [
            'form#FindACPABottinForm button[type="submit"]',
            'form#FindACPABottinForm input[type="submit"]',
            'a.button.radius[data-webform-action="Rechercher"]',
            'button[value*="SEARCH"]',
            'input[value*="SEARCH"]',
            'button:contains("SEARCH")',
            'input[type="submit"][value="SEARCH"]'
        ];

        let searchButton = null;
        let usedSelector = null;

        // Диагностика: проверяем, что на странице
        const pageInfo = await page.evaluate(() => {
            const form = document.querySelector('#FindACPABottinForm');
            const allButtons = document.querySelectorAll('button, input[type="submit"], a.button');
            const allForms = document.querySelectorAll('form');

            return {
                hasForm: !!form,
                formId: form ? form.id : null,
                buttonsCount: allButtons.length,
                formsCount: allForms.length,
                url: window.location.href,
                title: document.title
            };
        });

        console.log('📊 Диагностика страницы:', pageInfo);

        // Сначала ищем кнопки только внутри формы поиска CPA
        console.log('🔍 Ищем кнопку поиска в форме CPA...');
        for (const selector of searchSelectors.slice(0, 3)) { // Только первые 3 селектора для формы
            try {
                searchButton = await page.$(selector);
                if (searchButton) {
                    // Проверяем, что кнопка видима и кликабельна
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);
                        return rect.width > 0 && rect.height > 0 &&
                               style.visibility !== 'hidden' &&
                               style.display !== 'none' &&
                               style.opacity !== '0';
                    }, searchButton);

                    if (isVisible) {
                        usedSelector = selector;
                        console.log(`🎯 Найдена видимая кнопка поиска в форме: ${selector}`);
                        break;
                    } else {
                        console.log(`⚠️ Кнопка найдена, но не видима: ${selector}`);
                        searchButton = null;
                    }
                }
            } catch (e) {
                // Продолжаем поиск
            }
        }

        // Если не нашли в форме, ищем по всей странице
        if (!searchButton) {
            console.log('🔍 Ищем кнопку поиска по всей странице...');
            for (const selector of searchSelectors.slice(3)) { // Остальные селекторы
                try {
                    searchButton = await page.$(selector);
                    if (searchButton) {
                        const isVisible = await page.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none' &&
                                   style.opacity !== '0';
                        }, searchButton);

                        if (isVisible) {
                            usedSelector = selector;
                            console.log(`🎯 Найдена видимая кнопка поиска: ${selector}`);
                            break;
                        } else {
                            searchButton = null;
                        }
                    }
                } catch (e) {
                    // Продолжаем поиск
                }
            }
        }

        if (!searchButton) {
            console.log('⚠️ Кнопка поиска не найдена, пробуем JavaScript');
            // Улучшенный fallback через JavaScript
            const result = await page.evaluate(() => {
                console.log('🔍 JavaScript поиск кнопки...');

                // Метод 1: Ищем форму поиска CPA и кнопку в ней
                const form = document.querySelector('#FindACPABottinForm');
                if (form) {
                    console.log('✅ Найдена форма FindACPABottinForm');

                    // Ищем кнопку submit в форме
                    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitButton) {
                        const rect = submitButton.getBoundingClientRect();
                        const style = window.getComputedStyle(submitButton);
                        const isVisible = rect.width > 0 && rect.height > 0 &&
                                        style.visibility !== 'hidden' &&
                                        style.display !== 'none';

                        if (isVisible) {
                            console.log('✅ Найдена видимая кнопка submit в форме CPA');
                            submitButton.click();
                            return { success: true, method: 'form submit button' };
                        }
                    }

                    // Ищем ссылку с data-webform-action="Rechercher"
                    const searchLink = form.querySelector('a[data-webform-action="Rechercher"]');
                    if (searchLink) {
                        const rect = searchLink.getBoundingClientRect();
                        const style = window.getComputedStyle(searchLink);
                        const isVisible = rect.width > 0 && rect.height > 0 &&
                                        style.visibility !== 'hidden' &&
                                        style.display !== 'none';

                        if (isVisible) {
                            console.log('✅ Найдена ссылка поиска в форме CPA');
                            searchLink.click();
                            return { success: true, method: 'form search link' };
                        }
                    }

                    // Пробуем отправить форму напрямую
                    console.log('🔄 Отправляем форму CPA напрямую');
                    form.submit();
                    return { success: true, method: 'form submit' };
                }

                // Метод 2: Ищем кнопки с текстом SEARCH
                console.log('🔍 Ищем кнопки с текстом SEARCH...');
                const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"], a.button');
                for (const btn of allButtons) {
                    const text = btn.textContent || btn.value || btn.getAttribute('title') || '';
                    if (text.toLowerCase().includes('search') || text.toLowerCase().includes('rechercher')) {
                        const rect = btn.getBoundingClientRect();
                        const style = window.getComputedStyle(btn);
                        const isVisible = rect.width > 0 && rect.height > 0 &&
                                        style.visibility !== 'hidden' &&
                                        style.display !== 'none';

                        if (isVisible) {
                            console.log(`✅ Найдена кнопка поиска: ${text}`);
                            btn.click();
                            return { success: true, method: 'search button by text' };
                        }
                    }
                }

                console.log('❌ Не удалось найти подходящую кнопку поиска');
                return { success: false, method: 'none' };
            });

            if (result && result.success) {
                console.log(`✅ Кнопка поиска нажата через JavaScript (${result.method})`);
                await humanDelay(1000, 2000, 'после нажатия кнопки поиска');
                return true;
            } else {
                console.error('❌ Кнопка поиска не найдена даже через JavaScript');
                return false;
            }
        }

        // Человеческое взаимодействие с кнопкой
        console.log(`👆 Нажимаем кнопку поиска с человеческим поведением: ${usedSelector}`);

        // Прокручиваем к кнопке
        await searchButton.scrollIntoView();
        await humanDelay(300, 800, 'прокрутка к кнопке поиска');

        // Движение мыши к кнопке
        await humanMouseMovement(page, usedSelector);
        await humanDelay(500, 1500, 'наведение на кнопку поиска');

        // Финальная пауза перед кликом (имитация размышления)
        await humanDelay(800, 2000, 'размышление перед поиском');

        // Клик по кнопке с дополнительными опциями
        try {
            await searchButton.click();
            console.log('✅ Человеческий клик по кнопке поиска выполнен');
        } catch (error) {
            console.log(`⚠️ Обычный клик не сработал: ${error.message}, пробуем force click`);
            try {
                await searchButton.click({ force: true });
                console.log('✅ Force клик по кнопке поиска выполнен');
            } catch (error2) {
                console.log(`⚠️ Force клик не сработал: ${error2.message}, пробуем JavaScript`);
                await page.evaluate(el => el.click(), searchButton);
                console.log('✅ JavaScript клик по кнопке поиска выполнен');
            }
        }

        // Пауза после клика
        await humanDelay(1000, 2500, 'ожидание после нажатия поиска');

        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом нажатии кнопки поиска: ${error.message}`);
        return false;
    }
}

async function solveCaptchaAuto(page) {
    // Проверяем доступность сервисов капчи
    const has2Captcha = !!TWOCAPTCHA_KEY;
    const hasAntiCaptcha = !!ANTICAPTCHA_KEY;

    if (!has2Captcha && !hasAntiCaptcha) {
        console.log('⚠️ Ключи капча-сервисов не установлены, используем ручное решение');
        return await waitForCaptchaManual(page);
    }

    // Симулируем изучение страницы перед взаимодействием с капчей
    console.log('📖 Изучаем страницу перед решением капчи...');
    await simulateReadingPage(page, 2000, 5000);

    // Сначала кликаем по галочке "я не робот" с человеческим поведением
    console.log('👆 Кликаем по галочке "я не робот" с эмуляцией человека...');
    await clickRecaptchaCheckboxHuman(page);

    // Ждем с человеческими задержками и проверяем, может капча решилась автоматически
    await humanDelay(2000, 4000, 'ожидание после клика по капче');
    if (await isRecaptchaSolved(page)) {
        console.log('✅ Капча решена автоматически после клика!');
        return true;
    }

    // Извлекаем sitekey
    const { success: sitekeySuccess, sitekey } = await extractRecaptchaSitekey(page);
    if (!sitekeySuccess || !sitekey) {
        console.error('❌ Не удалось извлечь sitekey');
        return false;
    }

    // Пробуем сначала 2captcha, потом Anti-Captcha
    let taskSuccess = false;
    let taskId = null;
    let service = '';

    if (has2Captcha) {
        console.log('🔐 Пробуем 2captcha...');
        const result = await create2CaptchaTask(sitekey, BASE_URL);
        if (result.success) {
            taskSuccess = true;
            taskId = result.taskId;
            service = '2captcha';
        } else {
            console.log('⚠️ 2captcha не сработал, пробуем Anti-Captcha...');
        }
    }

    if (!taskSuccess && hasAntiCaptcha) {
        console.log('🔐 Используем Anti-Captcha...');
        const result = await createCaptchaTask(sitekey, BASE_URL);
        if (result.success) {
            taskSuccess = true;
            taskId = result.taskId;
            service = 'anticaptcha';
        }
    }

    if (!taskSuccess || !taskId) {
        console.error('❌ Не удалось создать задачу ни в одном сервисе');
        return await waitForCaptchaManual(page);
    }

    // Симулируем ожидание пользователя во время решения капчи
    console.log(`🎭 Симулируем человеческое поведение во время решения капчи (${service})...`);
    await simulateWaitingBehavior(page);

    // Получаем результат в зависимости от сервиса
    let resultSuccess = false;
    let token = null;

    if (service === '2captcha') {
        const result = await get2CaptchaResult(taskId);
        resultSuccess = result.success;
        token = result.token;
    } else {
        const result = await getCaptchaResult(taskId);
        resultSuccess = result.success;
        token = result.token;
    }

    if (!resultSuccess || !token) {
        console.error(`❌ Не удалось получить токен от ${service}`);
        return await waitForCaptchaManual(page);
    }

    // Вставляем токен с задержкой
    await humanDelay(1000, 2000, 'перед вставкой токена');
    if (!await insertCaptchaToken(page, token)) {
        console.error('❌ Не удалось вставить токен');
        return false;
    }

    // Проверяем, что капча решена с человеческой задержкой
    await humanDelay(1500, 3000, 'проверка решения капчи');
    if (await isRecaptchaSolved(page)) {
        console.log(`✅ Капча автоматически решена через ${service}!`);

        // Ждем исчезновения окна с визуальной капчей
        console.log('⏳ Ожидаем исчезновения окна капчи...');
        await waitForCaptchaWindowToClose(page);

        return true;
    } else {
        console.log('⚠️ Токен вставлен, но капча не считается решенной');
        // Пробуем еще раз кликнуть по галочке с человеческим поведением
        await clickRecaptchaCheckboxHuman(page);
        await humanDelay(2000, 4000, 'повторная проверка капчи');

        const solved = await isRecaptchaSolved(page);
        if (solved) {
            console.log('⏳ Ожидаем исчезновения окна капчи...');
            await waitForCaptchaWindowToClose(page);
        }
        return solved;
    }
}

async function waitForCaptchaManual(page) {
    console.log('⚠️ ВНИМАНИЕ: Обнаружена reCAPTCHA!');
    console.log('👤 Пожалуйста, решите капчу вручную в браузере и нажмите Enter для продолжения...');

    // Проверяем, есть ли уже решенная капча
    let token = await page.evaluate(() => {
        const response = document.querySelector('#g-recaptcha-response');
        return response && response.value && response.value.length > 50;
    });

    if (!token) {
        // Ждем ручного ввода
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        await new Promise(resolve => {
            rl.question('Нажмите Enter после решения капчи: ', () => {
                rl.close();
                resolve();
            });
        });

        // Проверяем еще раз
        token = await page.evaluate(() => {
            const response = document.querySelector('#g-recaptcha-response');
            return response && response.value && response.value.length > 50;
        });
    }

    if (token) {
        console.log('✅ Капча решена!');
        return true;
    } else {
        console.log('❌ Капча не решена');
        return false;
    }
}

async function handleCaptcha(page) {
    // Проверяем наличие капчи
    const captchaExists = await page.evaluate(() => {
        return document.querySelector('.g-recaptcha') !== null ||
               document.querySelector('iframe[src*="recaptcha"]') !== null;
    });

    if (!captchaExists) {
        console.log('ℹ️ Капча не найдена');
        return true;
    }

    // Пробуем автоматическое решение
    return await solveCaptchaAuto(page);
}

// === ФУНКЦИИ РАБОТЫ С ФОРМАМИ ===

async function closeCookiesBannerHuman(page) {
    try {
        const cookieBanner = await page.$('text=Tout refuser');
        if (cookieBanner) {
            console.log('🍪 Обнаружен cookie banner, закрываем с человеческим поведением...');

            // Симулируем чтение баннера
            await humanDelay(1000, 2500, 'чтение cookie banner');

            // Движение мыши к кнопке
            await humanMouseMovement(page, 'text=Tout refuser');
            await humanDelay(300, 800, 'наведение на кнопку отказа');

            // Клик
            await cookieBanner.click();
            console.log('✅ Cookie banner закрыт');

            await humanDelay(1000, 2000, 'после закрытия banner');
            return true;
        }
    } catch (error) {
        // Игнорируем ошибки
    }
    console.log('ℹ️ Cookie banner не найден или уже закрыт');
    return false;
}

async function selectMultipleCategories(page, categories) {
    console.log(`📋 Выбираем категории с человеческим поведением: ${categories.join(', ')}`);

    // Симулируем изучение формы перед выбором
    await simulateReadingPage(page, 1500, 3000);

    // ДИАГНОСТИКА: Анализируем реальную структуру формы
    console.log('🔍 Анализируем структуру формы категорий...');
    const formAnalysis = await page.evaluate(() => {
        // Ищем все чекбоксы категорий
        const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
        const categoryCheckboxes = [];
        
        allCheckboxes.forEach(checkbox => {
            const id = checkbox.id;
            const name = checkbox.name;
            const label = checkbox.closest('label') || document.querySelector(`label[for="${id}"]`);
            const labelText = label ? label.innerText.trim() : '';
            
            // Ищем чекбоксы, связанные с категориями клиентов
            if (id.includes('ListeClienteleDesservies') || 
                name.includes('ListeClienteleDesservies') ||
                labelText.includes('Individual') ||
                labelText.includes('SME') ||
                labelText.includes('Large') ||
                labelText.includes('Start-up') ||
                labelText.includes('Self-employed') ||
                labelText.includes('Professional') ||
                labelText.includes('Public') ||
                labelText.includes('Retailer') ||
                labelText.includes('NFPO') ||
                labelText.includes('Syndicate')) {
                
                categoryCheckboxes.push({
                    id: id,
                    name: name,
                    labelText: labelText,
                    checked: checkbox.checked,
                    visible: checkbox.offsetHeight > 0
                });
            }
        });

        return {
            totalCheckboxes: allCheckboxes.length,
            categoryCheckboxes: categoryCheckboxes,
            formExists: !!document.querySelector('#FindACPABottinForm')
        };
    });

    console.log('📊 Анализ формы:', JSON.stringify(formAnalysis, null, 2));

    if (formAnalysis.categoryCheckboxes.length === 0) {
        console.error('❌ Чекбоксы категорий не найдены на странице');
        return false;
    }

    // Создаем маппинг категорий на основе реальных данных
    const realCategoryMapping = {};
    
    // Сопоставляем наши категории с реальными чекбоксами
    for (const category of categories) {
        let foundCheckbox = null;
        
        // Сначала пробуем точное соответствие по ID из константы
        if (CLIENT_CATEGORIES[category]) {
            foundCheckbox = formAnalysis.categoryCheckboxes.find(cb => 
                cb.id === CLIENT_CATEGORIES[category] || 
                cb.id === CLIENT_CATEGORIES[category].replace('__Selected', '')
            );
        }
        
        // Если не найдено, ищем по тексту лейбла
        if (!foundCheckbox) {
            const searchTerms = {
                "Individuals": ["Individual"],
                "Large companies": ["Large", "companies"],
                "NFPOs": ["NFPO", "NFP"],
                "Professional firms": ["Professional", "firms"],
                "Public corporations": ["Public", "corporation"],
                "Retailers": ["Retailer"],
                "Self-employed workers": ["Self-employed", "Self employed"],
                "SMEs": ["SME", "Small", "Medium"],
                "Start-ups": ["Start-up", "Startup"],
                "Syndicates of co-owners": ["Syndicate", "co-owner"]
            };
            
            const terms = searchTerms[category] || [category];
            foundCheckbox = formAnalysis.categoryCheckboxes.find(cb => 
                terms.some(term => cb.labelText.toLowerCase().includes(term.toLowerCase()))
            );
        }
        
        if (foundCheckbox) {
            realCategoryMapping[category] = foundCheckbox.id;
            console.log(`✅ Сопоставлена категория "${category}" с чекбоксом ID: ${foundCheckbox.id}`);
        } else {
            console.error(`❌ Не найден чекбокс для категории "${category}"`);
        }
    }

    if (Object.keys(realCategoryMapping).length === 0) {
        console.error('❌ Не удалось сопоставить ни одной категории');
        return false;
    }

    // Сначала снимаем все чекбоксы категорий
    console.log('🔄 Сбрасываем все чекбоксы категорий...');
    await page.evaluate(() => {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            const id = checkbox.id;
            const name = checkbox.name;
            
            // Сбрасываем только чекбоксы категорий клиентов
            if (id.includes('ListeClienteleDesservies') || name.includes('ListeClienteleDesservies')) {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    // Вызываем события для корректной обработки формы
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    checkbox.dispatchEvent(new Event('click', { bubbles: true }));
                }
            }
        });
    });

    await humanDelay(500, 1000, 'после сброса чекбоксов');

    let successCount = 0;

    // Устанавливаем нужные чекбоксы с человеческим поведением
    for (let i = 0; i < categories.length; i++) {
        const category = categories[i];
        const checkboxId = realCategoryMapping[category];

        if (!checkboxId) {
            console.error(`❌ Не найден ID чекбокса для категории: ${category}`);
            continue;
        }

        console.log(`☑️ Выбираем категорию: ${category} (ID: ${checkboxId})`);

        try {
            // Ищем чекбокс по ID
            const checkbox = await page.$(`#${checkboxId}`);
            if (!checkbox) {
                console.error(`❌ Чекбокс не найден в DOM: ${checkboxId}`);
                continue;
            }

            // Проверяем видимость чекбокса
            const isVisible = await page.evaluate(el => {
                const rect = el.getBoundingClientRect();
                const style = window.getComputedStyle(el);
                return rect.width > 0 && rect.height > 0 &&
                       style.visibility !== 'hidden' &&
                       style.display !== 'none' &&
                       style.opacity !== '0';
            }, checkbox);

            if (!isVisible) {
                console.log(`⚠️ Чекбокс не видим, пробуем найти связанный лейбл: ${checkboxId}`);
                
                // Пробуем кликнуть по лейблу
                const labelClicked = await page.evaluate((id) => {
                    const checkbox = document.getElementById(id);
                    if (!checkbox) return false;
                    
                    // Ищем связанный лейбл
                    const label = checkbox.closest('label') || document.querySelector(`label[for="${id}"]`);
                    if (label) {
                        label.click();
                        return true;
                    }
                    
                    // Если лейбл не найден, пробуем кликнуть по родительскому элементу
                    const parent = checkbox.parentElement;
                    if (parent) {
                        parent.click();
                        return true;
                    }
                    
                    return false;
                }, checkboxId);

                if (!labelClicked) {
                    console.error(`❌ Не удалось кликнуть по лейблу для: ${checkboxId}`);
                    continue;
                }
            } else {
                // Прокручиваем к элементу с человеческим поведением
                await checkbox.scrollIntoView();
                await humanDelay(300, 800, `прокрутка к категории ${category}`);

                // Движение мыши к чекбоксу
                await humanMouseMovement(page, `#${checkboxId}`);
                await humanDelay(400, 1200, `изучение категории ${category}`);

                // Человеческий клик по чекбоксу
                await checkbox.click();
            }

            // Дополнительная проверка и принудительная установка
            const finalCheck = await page.evaluate((id) => {
                const checkbox = document.getElementById(id);
                if (!checkbox) return false;
                
                // Если чекбокс все еще не отмечен, принудительно отмечаем
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    // Вызываем все необходимые события
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    checkbox.dispatchEvent(new Event('click', { bubbles: true }));
                    checkbox.dispatchEvent(new Event('input', { bubbles: true }));
                }
                
                return checkbox.checked;
            }, checkboxId);

            if (finalCheck) {
                console.log(`✅ Категория '${category}' успешно выбрана`);
                successCount++;
            } else {
                console.error(`❌ Не удалось выбрать категорию '${category}' даже принудительно`);
            }

            // Пауза между выбором категорий (кроме последней)
            if (i < categories.length - 1) {
                await humanDelay(800, 2000, `пауза после выбора ${category}`);
            }

        } catch (error) {
            console.error(`❌ Ошибка при выборе категории '${category}': ${error.message}`);
        }
    }

    // Финальная проверка состояния всех чекбоксов
    console.log('🔍 Финальная проверка состояния чекбоксов...');
    const finalState = await page.evaluate(() => {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        const checkedBoxes = [];
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked && 
                (checkbox.id.includes('ListeClienteleDesservies') || 
                 checkbox.name.includes('ListeClienteleDesservies'))) {
                
                const label = checkbox.closest('label') || document.querySelector(`label[for="${checkbox.id}"]`);
                checkedBoxes.push({
                    id: checkbox.id,
                    labelText: label ? label.innerText.trim() : 'Без лейбла'
                });
            }
        });
        
        return checkedBoxes;
    });

    console.log('📊 Отмеченные чекбоксы:', finalState);

    // Финальная пауза для "обдумывания" выбора
    await humanDelay(1000, 2500, 'обдумывание выбранных категорий');

    console.log(`📊 Успешно выбрано ${successCount} из ${categories.length} категорий`);
    console.log(`📊 Реально отмечено ${finalState.length} чекбоксов на странице`);
    
    return successCount > 0;
}



async function extractResults(page) {
    console.log('📊 Извлекаем результаты поиска...');
    const results = [];

    // Ждем загрузки результатов
    try {
        await page.waitForSelector('.search-results, .results, .listing', { timeout: 30000 });
    } catch (error) {
        console.log('⚠️ Селектор результатов не найден, продолжаем');
    }

    // Пробуем различные селекторы для результатов профилей CPA
    const resultSelectors = [
        '.search-result',
        '.result-item',
        '.listing-item',
        '.cpa-listing',
        'article',
        '.card',
        'div.row:has(.cpa-name)',
        'div.row:has(a[href*="/profile/"])',
        'div.row:has(a[href*="/directory/"])',
        'div.row div.col:has(h3)',
        'div.row div.col:has(h2)',
        'div:has(strong):has(a[href*="mailto"])',
        'div:has(h3):has(a[href*="tel"])',
        'tr:has(td)',
        'li.profile',
        'li.member'
    ];

    let cards = null;
    let count = 0;

    // Анализируем страницу с помощью JavaScript
    const jsAnalysis = await page.evaluate(() => {
        // Ищем элементы, которые могут содержать профили CPA
        const possibleResults = [];

        // Метод 1: Поиск элементов с email
        const emailElements = Array.from(document.querySelectorAll('a[href^="mailto"]'));
        emailElements.forEach(elem => {
            const parent = elem.closest('div, tr, li, article');
            if (parent && !possibleResults.includes(parent)) {
                possibleResults.push(parent);
            }
        });

        // Метод 2: Поиск элементов с телефоном
        const phoneElements = Array.from(document.querySelectorAll('a[href^="tel"]'));
        phoneElements.forEach(elem => {
            const parent = elem.closest('div, tr, li, article');
            if (parent && !possibleResults.includes(parent)) {
                possibleResults.push(parent);
            }
        });

        return {
            emailElementsCount: emailElements.length,
            phoneElementsCount: phoneElements.length,
            possibleResultsCount: possibleResults.length,
            pageText: document.body.innerText.substring(0, 500)
        };
    });

    console.log('🔍 JS анализ:', jsAnalysis);

    for (const selector of resultSelectors) {
        try {
            const elements = await page.$$(selector);
            count = elements.length;

            if (count > 0) {
                console.log(`🎯 Пробуем селектор: ${selector}, найдено элементов: ${count}`);

                // Проверяем первый элемент на наличие контактной информации
                if (count > 0) {
                    const firstElement = elements[0];
                    let firstElementText = '';

                    try {
                        firstElementText = await page.evaluate(el => el.innerText, firstElement);
                    } catch (e) {
                        // Игнорируем ошибки
                    }

                    // Проверяем, содержит ли элемент контактную информацию
                    const hasContactInfo = (
                        firstElementText.includes('@') ||
                        firstElementText.includes('tel:') ||
                        firstElementText.includes('CPA') ||
                        /\(\d{3}\)/.test(firstElementText)
                    );

                    if (hasContactInfo) {
                        console.log(`✅ Найдены результаты с контактной информацией с селектором: ${selector}`);
                        cards = elements;
                        break;
        } else {
                        console.log(`⚠️ Элементы с селектором ${selector} не содержат контактной информации`);
                    }
                }
            }
        } catch (error) {
            console.log(`❌ Ошибка с селектором ${selector}: ${error.message}`);
            continue;
        }
    }

    if (!cards || count === 0) {
        console.log('⚠️ Результаты поиска с контактной информацией не найдены');
        // Попробуем найти хотя бы элементы с именами
        try {
            cards = await page.$$('div:has(h3), div:has(h2), div:has(strong)');
            count = cards.length;
            if (count > 0) {
                console.log(`📋 Найдено ${count} элементов с заголовками как резервный вариант`);
            }
        } catch (e) {
            // Игнорируем ошибки
        }
    }

    if (!cards || count === 0) {
        console.log('❌ Вообще никаких результатов не найдено');
        return results;
    }

    console.log(`🔄 Обрабатываем ${count} результатов...`);

    const maxResults = Math.min(count, 50); // Ограничиваем до 50 результатов

    for (let i = 0; i < maxResults; i++) {
        try {
            const card = cards[i];

            // Получаем весь текст карточки
            let cardText = '';
            try {
                cardText = await page.evaluate(el => el.innerText, card);
            } catch (e) {
                // Игнорируем ошибки
            }

            // Пропускаем элементы навигации и меню
            if (cardText && (
                cardText.includes('Menu') ||
                cardText.includes('General public') ||
                cardText.includes('Back') ||
                cardText.includes('Find a CPA') ||
                cardText.includes('SEARCH') ||
                cardText.includes('Home Se')
            )) {
                console.log(`⏭️ Пропускаем элемент навигации #${i+1}`);
                continue;
            }

            // Извлекаем данные
            const result = await extractCardData(page, card, cardText);

            // Создаем результат только если есть хотя бы имя или контактная информация
            if (result.name !== 'Unknown' || result.email || result.phone || (result.profileUrl && result.profileUrl.includes('/profile/'))) {
                results.push(result);
                console.log(`📝 Результат ${results.length}: ${result.name}`);
            } else {
                console.log(`⏭️ Пропускаем результат #${i+1} - недостаточно данных`);
            }

        } catch (error) {
            console.error(`❌ Ошибка при обработке результата ${i+1}: ${error.message}`);
        }
    }

    console.log(`✅ Извлечено ${results.length} валидных результатов из ${count} элементов`);
    return results;
}

async function extractCardData(page, card, cardText) {
    // Извлекаем имя
    let name = 'Unknown';
    const nameSelectors = ['h3', 'h2', 'h4', '.name', '.title', 'strong', 'b'];

    for (const nameSelector of nameSelectors) {
        try {
            const nameElement = await card.$(nameSelector);
            if (nameElement) {
                const nameText = await page.evaluate(el => el.innerText, nameElement);
                if (nameText && nameText.trim().length > 2 &&
                    !['menu', 'back', 'search', 'home', 'general public'].some(word =>
                        nameText.toLowerCase().includes(word))) {
                    name = nameText.trim();
                    break;
                }
            }
        } catch (e) {
            continue;
        }
    }

    // Извлекаем ссылку на профиль
    let profileUrl = null;
    const linkSelectors = [
        'a[href*="/profile/"]',
        'a[href*="/directory/"]',
        'a[href*="/cpa/"]',
        'a[href*="/member/"]'
    ];

    for (const linkSelector of linkSelectors) {
        try {
            const linkElement = await card.$(linkSelector);
            if (linkElement) {
                const href = await page.evaluate(el => el.getAttribute('href'), linkElement);
                if (href) {
                    if (href.startsWith('/')) {
                        profileUrl = `https://cpaquebec.ca${href}`;
                    } else if (href.startsWith('http')) {
                        profileUrl = href;
                    }
                    break;
                }
            }
        } catch (e) {
                continue;
        }
    }

    // Если не нашли специфичную ссылку, берем первую ссылку
    if (!profileUrl) {
        try {
            const linkElement = await card.$('a');
            if (linkElement) {
                const href = await page.evaluate(el => el.getAttribute('href'), linkElement);
                if (href && !href.startsWith('#') && !href.startsWith('javascript')) {
                    if (href.startsWith('/')) {
                        profileUrl = `https://cpaquebec.ca${href}`;
                    } else if (href.startsWith('http')) {
                        profileUrl = href;
                    }
                }
            }
        } catch (e) {
            // Игнорируем ошибки
        }
    }

    // Извлекаем email и телефон из текста карточки
    let email = '';
    let phone = '';

    if (cardText) {
        // Ищем email
        const emailMatch = cardText.match(/[\w.+-]+@[\w-]+\.[\w.-]+/);
        if (emailMatch) {
            email = emailMatch[0];
        }

        // Ищем телефон
        const phoneMatch = cardText.match(/(\+\d{1,2}\s?)?(\(\d{3}\)|\d{3})[\s.-]?\d{3}[\s.-]?\d{4}/);
        if (phoneMatch) {
            phone = phoneMatch[0];
        }
    }

    return {
        name,
        profileUrl,
        email,
        phone,
        text: cardText ? cardText.substring(0, 200) : ''
    };
}

// === ОСНОВНЫЕ ФУНКЦИИ ПАРСЕРА ===

async function processMultipleCategories(page, categories = null) {
    if (!categories) {
        // По умолчанию выбираем несколько популярных категорий
        categories = ['Individuals', 'SMEs', 'Self-employed workers'];
    }

    console.log(`🎯 === Обработка категорий с человеческим поведением: ${categories.join(', ')} ===`);

    // Переходим на страницу поиска с человеческим поведением
    console.log('🌐 Переходим на страницу поиска...');
    await page.goto(BASE_URL, { timeout: 60000, waitUntil: 'domcontentloaded' });

    // Симулируем первоначальное изучение страницы
    await simulateReadingPage(page, 3000, 6000);

    // Закрываем cookie banner с человеческим поведением
    await closeCookiesBannerHuman(page);

    // Выбираем несколько категорий с человеческим поведением
    if (!await selectMultipleCategories(page, categories)) {
        console.error('❌ Не удалось выбрать ни одной категории');
        return [];
    }

    // Обрабатываем капчу с человеческим поведением
    if (!await handleCaptcha(page)) {
        console.error('❌ Не удалось решить капчу');
        return [];
    }

    // Нажимаем кнопку поиска с человеческим поведением
    if (!await clickSearchButtonHuman(page)) {
        console.error('❌ Не удалось нажать кнопку поиска');
        return [];
    }

    // Ждем перехода на страницу результатов с человеческими задержками
    console.log('⏳ Ожидаем загрузку результатов...');
    await humanDelay(3000, 6000, 'ожидание результатов поиска');

    // Симулируем изучение страницы результатов
    await simulateReadingPage(page, 2000, 4000);

    // Извлекаем результаты
    const results = await extractResults(page);

    // Добавляем категории к каждому результату
    for (const result of results) {
        result.categories = categories;
    }

    console.log(`✅ Найдено ${results.length} результатов для категорий: ${categories.join(', ')}`);
    return results;
}

async function saveResults(results, filename = null) {
    if (!results || results.length === 0) {
        console.log('⚠️ Нет результатов для сохранения');
        return;
    }

    // Создаем папку output если её нет
    try {
        await fs.mkdir(OUTPUT_DIR, { recursive: true });
    } catch (e) {
        // Папка уже существует
    }

    if (!filename) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        filename = `cpa_results_${timestamp}.json`;
    }

    const filepath = path.join(OUTPUT_DIR, filename);

    try {
        await fs.writeFile(filepath, JSON.stringify(results, null, 2), 'utf8');
        console.log(`💾 Результаты сохранены в JSON: ${filepath}`);
        console.log(`📊 Всего записей: ${results.length}`);
    } catch (error) {
        console.error(`❌ Ошибка при сохранении JSON файла: ${error.message}`);
    }
}

async function saveResultsToExcel(results, outputDir = OUTPUT_DIR, filename = null) {
    if (!results || results.length === 0) {
        console.log('⚠️ Нет результатов для сохранения в Excel');
        return;
    }

    // Создаем папку output если её нет
    try {
        await fs.mkdir(outputDir, { recursive: true });
    } catch (e) {
        // Папка уже существует
    }

    if (!filename) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        filename = `cpa_results_${timestamp}.xlsx`;
    }

    const filepath = path.join(outputDir, filename);

    try {
        // Подготавливаем данные для Excel
        const excelData = results.map((result, index) => ({
            '№': index + 1,
            'Имя': result.name || 'Не указано',
            'Email': result.email || 'Не указан',
            'Телефон': result.phone || 'Не указан',
            'Ссылка на профиль': result.profileUrl || 'Не указана',
            'Категории': Array.isArray(result.categories) ? result.categories.join(', ') : (result.categories || 'Не указаны'),
            'Страница': result.pageNumber || 1,
            'Дополнительная информация': result.text ? result.text.substring(0, 200) + (result.text.length > 200 ? '...' : '') : 'Нет данных',
            'Дата парсинга': new Date().toLocaleString('ru-RU')
        }));

        // Создаем рабочую книгу
        const workbook = XLSX.utils.book_new();
        
        // Создаем лист с данными
        const worksheet = XLSX.utils.json_to_sheet(excelData);

        // Настраиваем ширину колонок
        const columnWidths = [
            { wch: 5 },   // №
            { wch: 25 },  // Имя
            { wch: 30 },  // Email
            { wch: 20 },  // Телефон
            { wch: 40 },  // Ссылка на профиль
            { wch: 25 },  // Категории
            { wch: 10 },  // Страница
            { wch: 50 },  // Дополнительная информация
            { wch: 20 }   // Дата парсинга
        ];
        worksheet['!cols'] = columnWidths;

        // Добавляем лист в книгу
        XLSX.utils.book_append_sheet(workbook, worksheet, 'CPA Results');

        // Создаем лист со статистикой
        const stats = {
            'Общее количество записей': results.length,
            'Записи с email': results.filter(r => r.email).length,
            'Записи с телефоном': results.filter(r => r.phone).length,
            'Записи с профилем': results.filter(r => r.profileUrl).length,
            'Дата создания отчета': new Date().toLocaleString('ru-RU')
        };

        const statsData = Object.entries(stats).map(([key, value]) => ({
            'Параметр': key,
            'Значение': value
        }));

        const statsWorksheet = XLSX.utils.json_to_sheet(statsData);
        statsWorksheet['!cols'] = [{ wch: 30 }, { wch: 20 }];
        XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'Статистика');

        // Создаем лист с категориями
        const categoriesStats = {};
        results.forEach(result => {
            if (result.categories) {
                const cats = Array.isArray(result.categories) ? result.categories : [result.categories];
                cats.forEach(cat => {
                    categoriesStats[cat] = (categoriesStats[cat] || 0) + 1;
                });
            }
        });

        const categoriesData = Object.entries(categoriesStats).map(([category, count]) => ({
            'Категория': category,
            'Количество записей': count,
            'Процент от общего': ((count / results.length) * 100).toFixed(1) + '%'
        }));

        if (categoriesData.length > 0) {
            const categoriesWorksheet = XLSX.utils.json_to_sheet(categoriesData);
            categoriesWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }, { wch: 20 }];
            XLSX.utils.book_append_sheet(workbook, categoriesWorksheet, 'По категориям');
        }

        // Сохраняем файл
        XLSX.writeFile(workbook, filepath);

        console.log(`📊 Результаты сохранены в Excel: ${filepath}`);
        console.log(`📈 Всего записей: ${results.length}`);
        console.log(`📧 С email: ${results.filter(r => r.email).length}`);
        console.log(`📞 С телефоном: ${results.filter(r => r.phone).length}`);
        console.log(`🔗 С профилем: ${results.filter(r => r.profileUrl).length}`);

        return filepath;

    } catch (error) {
        console.error(`❌ Ошибка при сохранении Excel файла: ${error.message}`);
        console.log('💡 Убедитесь, что установлена библиотека xlsx: npm install xlsx');
        throw error;
    }
}

// === ОСНОВНАЯ ФУНКЦИЯ ===

async function main() {
    console.log('🚀 CPA Quebec Parser с эмуляцией человеческого поведения (Puppeteer версия)');

    // Парсинг аргументов командной строки
    const args = process.argv.slice(2);
    const options = {
        visible: args.includes('--visible'),
        debug: args.includes('--debug'),
        slow: args.includes('--slow'),
        fast: args.includes('--fast'),
        byCategory: args.includes('--by-category')
    };

    // Получаем категории из аргументов
    let categories = null;
    const categoryIndex = args.indexOf('--category');
    const categoriesIndex = args.indexOf('--categories');

    if (categoryIndex !== -1 && args[categoryIndex + 1]) {
        categories = [args[categoryIndex + 1]];
    } else if (categoriesIndex !== -1) {
        categories = [];
        for (let i = categoriesIndex + 1; i < args.length; i++) {
            if (args[i].startsWith('--')) break;
            if (CLIENT_CATEGORIES[args[i]]) {
                categories.push(args[i]);
            }
        }
    }

    // Получаем максимальное количество страниц
    let maxPages = 10; // По умолчанию
    const maxPagesIndex = args.indexOf('--max-pages');
    if (maxPagesIndex !== -1 && args[maxPagesIndex + 1]) {
        const parsedMaxPages = parseInt(args[maxPagesIndex + 1]);
        if (!isNaN(parsedMaxPages) && parsedMaxPages > 0) {
            maxPages = parsedMaxPages;
        }
    }

    // Настраиваем глобальные множители задержек
    if (options.slow) {
        DELAY_MULTIPLIER = 2.0;
        console.log('🐌 Включен медленный режим (задержки x2)');
    } else if (options.fast) {
        DELAY_MULTIPLIER = 0.5;
        console.log('⚡ Включен быстрый режим (задержки x0.5)');
    } else {
        DELAY_MULTIPLIER = 1.0;
    }

    // Проверяем наличие ключей капча-сервисов
    if (TWOCAPTCHA_KEY) {
        console.log(`🔑 2captcha ключ найден: ${TWOCAPTCHA_KEY.substring(0, 5)}...${TWOCAPTCHA_KEY.slice(-5)}`);
    }
    if (ANTICAPTCHA_KEY) {
        console.log(`🔑 Anti-Captcha ключ найден: ${ANTICAPTCHA_KEY.substring(0, 5)}...${ANTICAPTCHA_KEY.slice(-5)}`);
    }
    if (!TWOCAPTCHA_KEY && !ANTICAPTCHA_KEY) {
        console.log('⚠️ Ключи капча-сервисов не найдены в .env файле. Будет использоваться ручное решение капчи.');
        console.log('💡 Добавьте TWOCAPTCHA_API_KEY или ANTICAPTCHA_API_KEY в .env файл для автоматического решения.');
    }

    // Запускаем браузер
    const browser = await puppeteer.launch({
        headless: !options.visible,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-infobars',
            '--ignore-certificate-errors',
            '--ignore-ssl-errors',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-dev-shm-usage',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-default-apps',
            '--disable-popup-blocking',
            '--disable-translate',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-back-forward-cache',
            '--disable-ipc-flooding-protection'
        ]
    });

    try {
        const page = await browser.newPage();

        // Настраиваем viewport и user agent
        await page.setViewport({ width: 1280, height: 800 });
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Настраиваем дополнительные заголовки
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Cache-Control': 'max-age=0'
        });

        let allResults = [];

        if (categories) {
            if (categories.length === 1) {
                // Парсим одну указанную категорию с пагинацией
                const results = await processSingleCategoryWithPagination(page, categories[0], maxPages);
                allResults.push(...results);
            } else {
                // Парсим каждую категорию отдельно (по одной за раз) с пагинацией
                for (const category of categories) {
                    console.log(`\n🎯 === Начинаем обработку категории: ${category} ===`);
                    const results = await processSingleCategoryWithPagination(page, category, maxPages);
                    allResults.push(...results);

                    // Пауза между категориями
                    if (categories.indexOf(category) < categories.length - 1) {
                        await humanDelay(5000, 8000, 'пауза между категориями');
                    }
                }
            }
        } else if (options.byCategory) {
            // Парсим все категории по одной с пагинацией
            const categoriesList = Object.keys(CLIENT_CATEGORIES);
            for (const category of categoriesList) {
                console.log(`\n🎯 === Начинаем обработку категории: ${category} ===`);
                const results = await processSingleCategoryWithPagination(page, category, maxPages);
                allResults.push(...results);

                // Пауза между категориями
                if (categoriesList.indexOf(category) < categoriesList.length - 1) {
                    await humanDelay(5000, 8000, 'пауза между категориями');
                }
            }
        } else {
            // По умолчанию парсим одну популярную категорию с пагинацией
            const defaultCategory = 'Individuals';
            console.log(`\n🎯 === Парсинг по умолчанию: ${defaultCategory} ===`);
            const results = await processSingleCategoryWithPagination(page, defaultCategory, maxPages);
            allResults.push(...results);
        }

        // Сохраняем результаты в Excel и JSON
        await saveResultsToExcel(allResults, OUTPUT_DIR);
        await saveResults(allResults);

        console.log(`\n🎉 Парсинг завершен! Всего найдено ${allResults.length} результатов.`);

    } catch (error) {
        console.error(`💥 Критическая ошибка: ${error.message}`);
        if (options.debug) {
            console.error(error.stack);
        }
    } finally {
        await browser.close();
    }
}

// Показываем справку если нужно
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🎯 CPA Quebec Parser с эмуляцией человеческого поведения и пагинацией (Puppeteer версия)
📋 ВАЖНО: Парсер выбирает ТОЛЬКО ОДНУ галочку за раз для более точных результатов
📄 НОВОЕ: Автоматическая обработка всех страниц результатов с пагинацией

Использование:
  node quebec_parser_puppeteer.js [опции]

Опции:
  --visible              Запуск в видимом режиме
  --debug                Отладочный режим
  --slow                 Медленный режим (максимальные задержки)
  --fast                 Быстрый режим (минимальные задержки)
  --category <название>  Парсинг одной категории
  --categories <список>  Парсинг нескольких категорий (по одной за раз)
  --by-category          Парсинг всех категорий (по одной за раз)
  --max-pages <число>    Максимальное количество страниц для парсинга (по умолчанию: 10)
  --help, -h             Показать эту справку

Доступные категории:
  ${Object.keys(CLIENT_CATEGORIES).join(', ')}

Примеры:
  node quebec_parser_puppeteer.js --visible
  node quebec_parser_puppeteer.js --visible --slow --category "Individuals"
  node quebec_parser_puppeteer.js --visible --categories "Individuals" "SMEs" "Start-ups"
  node quebec_parser_puppeteer.js --visible --by-category
  node quebec_parser_puppeteer.js --visible --category "SMEs" --max-pages 5
  node quebec_parser_puppeteer.js --visible --fast --category "Individuals" --max-pages 20

Логика работы:
  • По умолчанию: выбирается категория "Individuals"
  • При --category: выбирается указанная категория
  • При --categories: каждая категория обрабатывается отдельно
  • При --by-category: все категории обрабатываются по очереди
  • В каждом запросе выбирается ТОЛЬКО ОДНА галочка
  • Автоматически обрабатываются ВСЕ страницы результатов

Пагинация:
  • Автоматическое обнаружение кнопок "Next" / "Suivant"
  • Человеческие задержки между страницами
  • Остановка при достижении последней страницы
  • Добавление номера страницы к каждому результату в Excel

Переменные окружения:
  TWOCAPTCHA_API_KEY     Ключ для 2captcha (приоритетный)
  ANTICAPTCHA_API_KEY    Ключ для Anti-Captcha (резервный)
`);
    process.exit(0);
}

// Запускаем парсер
if (require.main === module) {
    main().catch(error => {
        console.error(`💥 Фатальная ошибка: ${error.message}`);
        process.exit(1);
    });
}

// === ФУНКЦИЯ ТЕСТИРОВАНИЯ ВЫБОРА КАТЕГОРИЙ ===

async function testCategorySelection(page) {
    console.log('🧪 === ТЕСТИРОВАНИЕ ВЫБОРА КАТЕГОРИЙ ===');
    
    // Анализируем текущее состояние формы
    const currentState = await page.evaluate(() => {
        const form = document.querySelector('#FindACPABottinForm');
        if (!form) {
            return { error: 'Форма не найдена' };
        }
        
        const allCheckboxes = form.querySelectorAll('input[type="checkbox"]');
        const categoryCheckboxes = [];
        
        allCheckboxes.forEach(checkbox => {
            const id = checkbox.id;
            const name = checkbox.name;
            const label = checkbox.closest('label') || document.querySelector(`label[for="${id}"]`);
            const labelText = label ? label.innerText.trim() : '';
            
            if (id.includes('ListeClienteleDesservies') || name.includes('ListeClienteleDesservies')) {
                categoryCheckboxes.push({
                    id: id,
                    name: name,
                    labelText: labelText,
                    checked: checkbox.checked,
                    visible: checkbox.offsetHeight > 0,
                    enabled: !checkbox.disabled
                });
            }
        });
        
        return {
            formExists: true,
            totalCheckboxes: allCheckboxes.length,
            categoryCheckboxes: categoryCheckboxes,
            checkedCount: categoryCheckboxes.filter(cb => cb.checked).length
        };
    });
    
    console.log('📊 Текущее состояние формы:', JSON.stringify(currentState, null, 2));
    
    if (currentState.error) {
        console.error('❌ Ошибка при анализе формы:', currentState.error);
        return false;
    }
    
    if (currentState.categoryCheckboxes.length === 0) {
        console.error('❌ Чекбоксы категорий не найдены');
        return false;
    }
    
    // Тестируем выбор первых 3 категорий
    const testCategories = ['Individuals', 'SMEs', 'Self-employed workers'];
    console.log(`🎯 Тестируем выбор категорий: ${testCategories.join(', ')}`);
    
    const success = await selectMultipleCategories(page, testCategories);
    
    if (success) {
        console.log('✅ Тест выбора категорий прошел успешно');
        
        // Проверяем финальное состояние
        const finalState = await page.evaluate(() => {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const checkedBoxes = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked && 
                    (checkbox.id.includes('ListeClienteleDesservies') || 
                     checkbox.name.includes('ListeClienteleDesservies'))) {
                    
                    const label = checkbox.closest('label') || document.querySelector(`label[for="${checkbox.id}"]`);
                    checkedBoxes.push({
                        id: checkbox.id,
                        labelText: label ? label.innerText.trim() : 'Без лейбла'
                    });
                }
            });
            
            return checkedBoxes;
        });
        
        console.log('📊 Финальное состояние после теста:', finalState);
        return finalState.length >= 2; // Ожидаем минимум 2 отмеченных чекбокса
    } else {
        console.error('❌ Тест выбора категорий провалился');
        return false;
    }
}

// === ФУНКЦИЯ ВЫБОРА ОДНОЙ КАТЕГОРИИ ===

async function selectSingleCategory(page, category) {
    console.log(`📋 Выбираем одну категорию с человеческим поведением: ${category}`);

    // Симулируем изучение формы перед выбором
    await simulateReadingPage(page, 1500, 3000);

    // ДИАГНОСТИКА: Анализируем реальную структуру формы
    console.log('🔍 Анализируем структуру формы категорий...');
    const formAnalysis = await page.evaluate(() => {
        // Ищем все чекбоксы категорий
        const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
        const categoryCheckboxes = [];
        
        allCheckboxes.forEach(checkbox => {
            const id = checkbox.id;
            const name = checkbox.name;
            const label = checkbox.closest('label') || document.querySelector(`label[for="${id}"]`);
            const labelText = label ? label.innerText.trim() : '';
            
            // Ищем чекбоксы, связанные с категориями клиентов
            if (id.includes('ListeClienteleDesservies') || 
                name.includes('ListeClienteleDesservies') ||
                labelText.includes('Individual') ||
                labelText.includes('SME') ||
                labelText.includes('Large') ||
                labelText.includes('Start-up') ||
                labelText.includes('Self-employed') ||
                labelText.includes('Professional') ||
                labelText.includes('Public') ||
                labelText.includes('Retailer') ||
                labelText.includes('NFPO') ||
                labelText.includes('Syndicate')) {
                
                categoryCheckboxes.push({
                    id: id,
                    name: name,
                    labelText: labelText,
                    checked: checkbox.checked,
                    visible: checkbox.offsetHeight > 0
                });
            }
        });

        return {
            totalCheckboxes: allCheckboxes.length,
            categoryCheckboxes: categoryCheckboxes,
            formExists: !!document.querySelector('#FindACPABottinForm')
        };
    });

    console.log('📊 Анализ формы:', JSON.stringify(formAnalysis, null, 2));

    if (formAnalysis.categoryCheckboxes.length === 0) {
        console.error('❌ Чекбоксы категорий не найдены на странице');
        return false;
    }

    // Находим нужный чекбокс для категории
    let foundCheckbox = null;
    
    // Сначала пробуем точное соответствие по ID из константы
    if (CLIENT_CATEGORIES[category]) {
        foundCheckbox = formAnalysis.categoryCheckboxes.find(cb => 
            cb.id === CLIENT_CATEGORIES[category] || 
            cb.id === CLIENT_CATEGORIES[category].replace('__Selected', '')
        );
    }
    
    // Если не найдено, ищем по тексту лейбла
    if (!foundCheckbox) {
        const searchTerms = {
            "Individuals": ["Individual"],
            "Large companies": ["Large", "companies"],
            "NFPOs": ["NFPO", "NFP"],
            "Professional firms": ["Professional", "firms"],
            "Public corporations": ["Public", "corporation"],
            "Retailers": ["Retailer"],
            "Self-employed workers": ["Self-employed", "Self employed"],
            "SMEs": ["SME", "Small", "Medium"],
            "Start-ups": ["Start-up", "Startup"],
            "Syndicates of co-owners": ["Syndicate", "co-owner"]
        };
        
        const terms = searchTerms[category] || [category];
        foundCheckbox = formAnalysis.categoryCheckboxes.find(cb => 
            terms.some(term => cb.labelText.toLowerCase().includes(term.toLowerCase()))
        );
    }
    
    if (!foundCheckbox) {
        console.error(`❌ Не найден чекбокс для категории "${category}"`);
        return false;
    }

    const checkboxId = foundCheckbox.id;
    console.log(`✅ Найден чекбокс для категории "${category}": ${checkboxId}`);

    // ВАЖНО: Сначала снимаем ВСЕ чекбоксы категорий
    console.log('🔄 Сбрасываем все чекбоксы категорий...');
    await page.evaluate(() => {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            const id = checkbox.id;
            const name = checkbox.name;
            
            // Сбрасываем только чекбоксы категорий клиентов
            if (id.includes('ListeClienteleDesservies') || name.includes('ListeClienteleDesservies')) {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    // Вызываем события для корректной обработки формы
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    checkbox.dispatchEvent(new Event('click', { bubbles: true }));
                }
            }
        });
    });

    await humanDelay(500, 1000, 'после сброса всех чекбоксов');

    // Теперь выбираем ТОЛЬКО нужную категорию
    console.log(`☑️ Выбираем единственную категорию: ${category} (ID: ${checkboxId})`);

    try {
        // Ищем чекбокс по ID
        const checkbox = await page.$(`#${checkboxId}`);
        if (!checkbox) {
            console.error(`❌ Чекбокс не найден в DOM: ${checkboxId}`);
            return false;
        }

        // Проверяем видимость чекбокса
        const isVisible = await page.evaluate(el => {
            const rect = el.getBoundingClientRect();
            const style = window.getComputedStyle(el);
            return rect.width > 0 && rect.height > 0 &&
                   style.visibility !== 'hidden' &&
                   style.display !== 'none' &&
                   style.opacity !== '0';
        }, checkbox);

        if (!isVisible) {
            console.log(`⚠️ Чекбокс не видим, пробуем найти связанный лейбл: ${checkboxId}`);
            
            // Пробуем кликнуть по лейблу
            const labelClicked = await page.evaluate((id) => {
                const checkbox = document.getElementById(id);
                if (!checkbox) return false;
                
                // Ищем связанный лейбл
                const label = checkbox.closest('label') || document.querySelector(`label[for="${id}"]`);
                if (label) {
                    label.click();
                    return true;
                }
                
                // Если лейбл не найден, пробуем кликнуть по родительскому элементу
                const parent = checkbox.parentElement;
                if (parent) {
                    parent.click();
                    return true;
                }
                
                return false;
            }, checkboxId);

            if (!labelClicked) {
                console.error(`❌ Не удалось кликнуть по лейблу для: ${checkboxId}`);
                return false;
            }
        } else {
            // Прокручиваем к элементу с человеческим поведением
            await checkbox.scrollIntoView();
            await humanDelay(300, 800, `прокрутка к категории ${category}`);

            // Движение мыши к чекбоксу
            await humanMouseMovement(page, `#${checkboxId}`);
            await humanDelay(400, 1200, `изучение категории ${category}`);

            // Человеческий клик по чекбоксу
            await checkbox.click();
        }

        // Дополнительная проверка и принудительная установка
        const finalCheck = await page.evaluate((id) => {
            const checkbox = document.getElementById(id);
            if (!checkbox) return false;
            
            // Если чекбокс все еще не отмечен, принудительно отмечаем
            if (!checkbox.checked) {
                checkbox.checked = true;
                // Вызываем все необходимые события
                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                checkbox.dispatchEvent(new Event('click', { bubbles: true }));
                checkbox.dispatchEvent(new Event('input', { bubbles: true }));
            }
            
            return checkbox.checked;
        }, checkboxId);

        if (!finalCheck) {
            console.error(`❌ Не удалось выбрать категорию '${category}' даже принудительно`);
            return false;
        }

        console.log(`✅ Категория '${category}' успешно выбрана`);

    } catch (error) {
        console.error(`❌ Ошибка при выборе категории '${category}': ${error.message}`);
        return false;
    }

    // Финальная проверка состояния всех чекбоксов
    console.log('🔍 Финальная проверка состояния чекбоксов...');
    const finalState = await page.evaluate(() => {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        const checkedBoxes = [];
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked && 
                (checkbox.id.includes('ListeClienteleDesservies') || 
                 checkbox.name.includes('ListeClienteleDesservies'))) {
                
                const label = checkbox.closest('label') || document.querySelector(`label[for="${checkbox.id}"]`);
                checkedBoxes.push({
                    id: checkbox.id,
                    labelText: label ? label.innerText.trim() : 'Без лейбла'
                });
            }
        });
        
        return checkedBoxes;
    });

    console.log('📊 Отмеченные чекбоксы:', finalState);

    // Проверяем, что выбрана только одна галочка
    if (finalState.length === 1) {
        console.log(`✅ Успешно выбрана ОДНА категория: ${finalState[0].labelText}`);
    } else if (finalState.length === 0) {
        console.error('❌ Ни одна категория не выбрана');
        return false;
    } else {
        console.warn(`⚠️ Выбрано ${finalState.length} категорий вместо одной`);
    }

    // Финальная пауза для "обдумывания" выбора
    await humanDelay(1000, 2500, 'обдумывание выбранной категории');

    return true;
}

// === ФУНКЦИЯ ОБРАБОТКИ ОДНОЙ КАТЕГОРИИ ===

async function processSingleCategory(page, category = null) {
    if (!category) {
        // По умолчанию выбираем популярную категорию
        category = 'Individuals';
    }

    console.log(`🎯 === Обработка одной категории с человеческим поведением: ${category} ===`);

    // Переходим на страницу поиска с человеческим поведением
    console.log('🌐 Переходим на страницу поиска...');
    await page.goto(BASE_URL, { timeout: 60000, waitUntil: 'domcontentloaded' });

    // Симулируем первоначальное изучение страницы
    await simulateReadingPage(page, 3000, 6000);

    // Закрываем cookie banner с человеческим поведением
    await closeCookiesBannerHuman(page);

    // Выбираем ОДНУ категорию с человеческим поведением
    if (!await selectSingleCategory(page, category)) {
        console.error('❌ Не удалось выбрать категорию');
        return [];
    }

    // Обрабатываем капчу с человеческим поведением
    if (!await handleCaptcha(page)) {
        console.error('❌ Не удалось решить капчу');
        return [];
    }

    // Нажимаем кнопку поиска с человеческим поведением
    if (!await clickSearchButtonHuman(page)) {
        console.error('❌ Не удалось нажать кнопку поиска');
        return [];
    }

    // Ждем перехода на страницу результатов с человеческими задержками
    console.log('⏳ Ожидаем загрузку результатов...');
    await humanDelay(3000, 6000, 'ожидание результатов поиска');

    // Симулируем изучение страницы результатов
    await simulateReadingPage(page, 2000, 4000);

    // Извлекаем результаты
    const results = await extractResults(page);

    // Добавляем категорию к каждому результату
    for (const result of results) {
        result.categories = [category]; // Только одна категория
    }

    console.log(`✅ Найдено ${results.length} результатов для категории: ${category}`);
    return results;
}

// === ФУНКЦИИ РАБОТЫ С ПАГИНАЦИЕЙ ===

async function checkPagination(page) {
    try {
        console.log('🔍 Проверяем наличие пагинации...');
        
        const paginationInfo = await page.evaluate(() => {
            // Ищем различные элементы пагинации
            const paginationSelectors = [
                '.pagination',
                '.pager',
                '.page-navigation',
                'nav[aria-label*="pagination"]',
                'nav[aria-label*="page"]',
                '.page-numbers',
                '.page-links',
                'ul.pagination',
                '.paging'
            ];
            
            let paginationContainer = null;
            let usedSelector = null;
            
            for (const selector of paginationSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    paginationContainer = element;
                    usedSelector = selector;
                    break;
                }
            }
            
            if (!paginationContainer) {
                return { hasPagination: false, reason: 'Контейнер пагинации не найден' };
            }
            
            // Ищем кнопки "Следующая страница"
            const nextButtonSelectors = [
                'a[aria-label*="Next"]',
                'a[title*="Next"]',
                'a:contains("Next")',
                'a:contains(">")',
                'a:contains("→")',
                'a:contains("Suivant")', // Французский
                'button[aria-label*="Next"]',
                'button:contains("Next")',
                '.next',
                '.page-next',
                'a[rel="next"]'
            ];
            
            let nextButton = null;
            let nextSelector = null;
            
            for (const selector of nextButtonSelectors) {
                try {
                    // Для селекторов с :contains используем альтернативный поиск
                    if (selector.includes(':contains')) {
                        const baseSelector = selector.split(':contains')[0];
                        const searchText = selector.match(/\("([^"]+)"\)/)?.[1];
                        
                        if (searchText) {
                            const elements = paginationContainer.querySelectorAll(baseSelector);
                            for (const el of elements) {
                                if (el.textContent.includes(searchText)) {
                                    nextButton = el;
                                    nextSelector = selector;
                                    break;
                                }
                            }
                        }
                    } else {
                        nextButton = paginationContainer.querySelector(selector);
                        if (nextButton) {
                            nextSelector = selector;
                            break;
                        }
                    }
                } catch (e) {
                    // Продолжаем поиск
                }
            }
            
            // Ищем информацию о текущей странице
            const pageInfoSelectors = [
                '.page-info',
                '.pagination-info',
                '.current-page',
                '.page-current',
                'span.current',
                '.active'
            ];
            
            let currentPageInfo = null;
            for (const selector of pageInfoSelectors) {
                const element = paginationContainer.querySelector(selector);
                if (element) {
                    currentPageInfo = element.textContent.trim();
                    break;
                }
            }
            
            // Подсчитываем общее количество страниц
            const pageLinks = paginationContainer.querySelectorAll('a[href*="page"], a[href*="Page"]');
            const pageNumbers = Array.from(pageLinks)
                .map(link => {
                    const match = link.textContent.match(/\d+/);
                    return match ? parseInt(match[0]) : 0;
                })
                .filter(num => num > 0);
            
            const maxPage = pageNumbers.length > 0 ? Math.max(...pageNumbers) : 1;
            
            return {
                hasPagination: !!nextButton,
                paginationSelector: usedSelector,
                nextButtonSelector: nextSelector,
                nextButtonExists: !!nextButton,
                nextButtonEnabled: nextButton ? !nextButton.disabled && !nextButton.classList.contains('disabled') : false,
                currentPageInfo: currentPageInfo,
                totalPages: maxPage,
                pageLinks: pageNumbers.length,
                nextButtonText: nextButton ? nextButton.textContent.trim() : null
            };
        });
        
        console.log('📊 Информация о пагинации:', paginationInfo);
        return paginationInfo;
        
    } catch (error) {
        console.error(`❌ Ошибка при проверке пагинации: ${error.message}`);
        return { hasPagination: false, reason: `Ошибка: ${error.message}` };
    }
}

async function goToNextPage(page) {
    try {
        console.log('➡️ Переходим на следующую страницу...');
        
        // Сначала проверяем пагинацию
        const paginationInfo = await checkPagination(page);
        
        if (!paginationInfo.hasPagination || !paginationInfo.nextButtonExists) {
            console.log('⚠️ Кнопка "Следующая страница" не найдена');
            return false;
        }
        
        if (!paginationInfo.nextButtonEnabled) {
            console.log('⚠️ Кнопка "Следующая страница" отключена (последняя страница)');
            return false;
        }
        
        // Получаем текущий URL для сравнения
        const currentUrl = page.url();
        
        // Симулируем человеческое поведение перед кликом
        await humanDelay(1000, 2500, 'изучение пагинации');
        
        // Прокручиваем к области пагинации
        await page.evaluate((selector) => {
            const paginationContainer = document.querySelector(selector);
            if (paginationContainer) {
                paginationContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, paginationInfo.paginationSelector);
        
        await humanDelay(500, 1000, 'прокрутка к пагинации');
        
        // Кликаем по кнопке "Следующая страница"
        const nextClicked = await page.evaluate((selector) => {
            const paginationContainer = document.querySelector(selector);
            if (!paginationContainer) return false;
            
            // Ищем кнопку следующей страницы
            const nextButtonSelectors = [
                'a[aria-label*="Next"]',
                'a[title*="Next"]',
                'a[rel="next"]',
                'button[aria-label*="Next"]',
                '.next',
                '.page-next'
            ];
            
            for (const sel of nextButtonSelectors) {
                const button = paginationContainer.querySelector(sel);
                if (button && !button.disabled && !button.classList.contains('disabled')) {
                    button.click();
                    return true;
                }
            }
            
            // Альтернативный поиск по тексту
            const allLinks = paginationContainer.querySelectorAll('a, button');
            for (const link of allLinks) {
                const text = link.textContent.trim().toLowerCase();
                if (text.includes('next') || text.includes('suivant') || text === '>' || text === '→') {
                    if (!link.disabled && !link.classList.contains('disabled')) {
                        link.click();
                        return true;
                    }
                }
            }
            
            return false;
        }, paginationInfo.paginationSelector);
        
        if (!nextClicked) {
            console.log('❌ Не удалось кликнуть по кнопке следующей страницы');
            return false;
        }
        
        console.log('✅ Клик по кнопке "Следующая страница" выполнен');
        
        // Ждем загрузки новой страницы
        await humanDelay(2000, 4000, 'ожидание загрузки следующей страницы');
        
        // Проверяем, изменился ли URL
        const newUrl = page.url();
        if (newUrl === currentUrl) {
            console.log('⚠️ URL не изменился, возможно это последняя страница');
            return false;
        }
        
        console.log(`✅ Успешно перешли на следующую страницу: ${newUrl}`);
        
        // Ждем загрузки контента
        try {
            await page.waitForSelector('.search-results, .results, .listing', { timeout: 10000 });
        } catch (e) {
            console.log('⚠️ Селектор результатов не найден на новой странице, но продолжаем');
        }
        
        // Симулируем изучение новой страницы
        await simulateReadingPage(page, 1000, 3000);
        
        return true;
        
    } catch (error) {
        console.error(`❌ Ошибка при переходе на следующую страницу: ${error.message}`);
        return false;
    }
}

async function extractAllPagesResults(page, maxPages = 10) {
    console.log(`📄 === Начинаем извлечение результатов со всех страниц (максимум ${maxPages}) ===`);
    
    let allResults = [];
    let currentPage = 1;
    
    while (currentPage <= maxPages) {
        console.log(`\n📄 === Обрабатываем страницу ${currentPage} ===`);
        
        // Извлекаем результаты с текущей страницы
        const pageResults = await extractResults(page);
        
        if (pageResults.length === 0) {
            console.log('⚠️ На текущей странице нет результатов, завершаем парсинг');
            break;
        }
        
        // Добавляем номер страницы к каждому результату
        pageResults.forEach(result => {
            result.pageNumber = currentPage;
        });
        
        allResults.push(...pageResults);
        console.log(`✅ Извлечено ${pageResults.length} результатов со страницы ${currentPage}`);
        console.log(`📊 Всего результатов: ${allResults.length}`);
        
        // Проверяем, есть ли следующая страница
        const paginationInfo = await checkPagination(page);
        
        if (!paginationInfo.hasPagination || !paginationInfo.nextButtonEnabled) {
            console.log('✅ Достигнута последняя страница');
            break;
        }
        
        // Переходим на следующую страницу
        const nextPageSuccess = await goToNextPage(page);
        
        if (!nextPageSuccess) {
            console.log('⚠️ Не удалось перейти на следующую страницу, завершаем парсинг');
            break;
        }
        
        currentPage++;
        
        // Пауза между страницами для имитации человеческого поведения
        await humanDelay(2000, 5000, `пауза между страницами ${currentPage-1} и ${currentPage}`);
    }
    
    console.log(`\n📊 === Завершено извлечение со всех страниц ===`);
    console.log(`📄 Обработано страниц: ${currentPage}`);
    console.log(`📋 Всего результатов: ${allResults.length}`);
    
    return allResults;
}

// === ФУНКЦИЯ ОБРАБОТКИ ОДНОЙ КАТЕГОРИИ С ПАГИНАЦИЕЙ ===

async function processSingleCategoryWithPagination(page, category = null, maxPages = 10) {
    if (!category) {
        // По умолчанию выбираем популярную категорию
        category = 'Individuals';
    }

    console.log(`🎯 === Обработка одной категории с пагинацией: ${category} (макс. ${maxPages} страниц) ===`);

    // Переходим на страницу поиска с человеческим поведением
    console.log('🌐 Переходим на страницу поиска...');
    await page.goto(BASE_URL, { timeout: 60000, waitUntil: 'domcontentloaded' });

    // Симулируем первоначальное изучение страницы
    await simulateReadingPage(page, 3000, 6000);

    // Закрываем cookie banner с человеческим поведением
    await closeCookiesBannerHuman(page);

    // Выбираем ОДНУ категорию с человеческим поведением
    if (!await selectSingleCategory(page, category)) {
        console.error('❌ Не удалось выбрать категорию');
        return [];
    }

    // Обрабатываем капчу с человеческим поведением
    if (!await handleCaptcha(page)) {
        console.error('❌ Не удалось решить капчу');
        return [];
    }

    // Нажимаем кнопку поиска с человеческим поведением
    if (!await clickSearchButtonHuman(page)) {
        console.error('❌ Не удалось нажать кнопку поиска');
        return [];
    }

    // Ждем перехода на страницу результатов с человеческими задержками
    console.log('⏳ Ожидаем загрузку результатов...');
    await humanDelay(3000, 6000, 'ожидание результатов поиска');

    // Симулируем изучение страницы результатов
    await simulateReadingPage(page, 2000, 4000);

    // Извлекаем результаты со всех страниц с пагинацией
    const results = await extractAllPagesResults(page, maxPages);

    // Добавляем категорию к каждому результату
    for (const result of results) {
        result.categories = [category]; // Только одна категория
    }

    console.log(`✅ Найдено ${results.length} результатов для категории: ${category}`);
    return results;
}
