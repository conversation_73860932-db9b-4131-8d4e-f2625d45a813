#!/usr/bin/env python3
"""
Тестовый парсер CPA Quebec Directory - с выбором категории "Individuals"
"""
import asyncio
import json
import logging
import os
import random
import time
from datetime import datetime
from pathlib import Path

import aiohttp
from dotenv import load_dotenv
from playwright.async_api import async_playwright

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("test_quebec_parser")

# Константы
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"
OUTPUT_DIR = Path("output")
ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")
TWOCAPTCHA_KEY = "7b6b7b9284632f959c37226eb1887231"  # Ваш API ключ 2captcha

# Глобальная сессия для Anti-Captcha и 2captcha
anticaptcha_session = None
twocaptcha_session = None

async def get_anticaptcha_session():
    """Получает или создает сессию для Anti-Captcha API"""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session

async def get_twocaptcha_session():
    """Получает или создает сессию для 2captcha API"""
    global twocaptcha_session
    if twocaptcha_session is None or twocaptcha_session.closed:
        twocaptcha_session = aiohttp.ClientSession()
    return twocaptcha_session

async def anticaptcha_request(method: str, **payload):
    """Выполняет запрос к Anti-Captcha API"""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()

    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY

    logger.debug(f"Anti-Captcha request: {method}, payload_keys: {list(payload.keys())}")
    try:
        async with session.post(url, json=payload, timeout=30) as resp:
            # Логируем HTTP статус
            logger.debug(f"Anti-Captcha response status ({method}): {resp.status}")

            response_json = await resp.json()
            # Логируем полный ответ API
            logger.debug(f"Anti-Captcha full response ({method}): {json.dumps(response_json, indent=2)}")

            # Проверяем на ошибки в ответе JSON, даже если статус HTTP 200
            if response_json.get("errorId", 0) != 0:
                error_code = response_json.get("errorCode", "UNKNOWN_API_ERROR")
                error_description = response_json.get("errorDescription", "No description from API.")
                logger.error(f"Anti-Captcha API error in response ({method}): {error_code} - {error_description}")
                # Возвращаем структуру ошибки, чтобы ее можно было обработать выше
                return {"errorId": response_json["errorId"], "errorCode": error_code, "errorDescription": error_description}

            resp.raise_for_status() # Это вызовет исключение для HTTP ошибок 4xx/5xx
            return response_json
    except aiohttp.ClientResponseError as e:
        logger.error(f"Anti-Captcha HTTP error ({method}, status={e.status}): {e.message}")
        error_body = await e.response.text() if e.response else "No response body"
        logger.error(f"Anti-Captcha HTTP error body: {error_body}")
        return {"errorId": 1, "errorCode": f"HTTP_{e.status}", "errorDescription": f"{e.message} - Body: {error_body[:200]}"}
    except asyncio.TimeoutError:
        logger.error(f"Anti-Captcha API timeout ({method})")
        return {"errorId": 1, "errorCode": "TIMEOUT_ERROR", "errorDescription": "API request timed out"}
    except Exception as e:
        logger.error(f"Generic error in Anti-Captcha API request ({method}): {e}", exc_info=True)
        return {"errorId": 1, "errorCode": "REQUEST_EXCEPTION", "errorDescription": str(e)}

async def check_anticaptcha_balance():
    """Проверяет баланс Anti-Captcha."""
    if not ANTICAPTCHA_KEY:
        logger.debug("ANTICAPTCHA_KEY не установлен, проверка баланса пропущена.")
        return True # Считаем, что все в порядке, если ключ не используется

    logger.info("Проверка баланса Anti-Captcha...")
    response = await anticaptcha_request("getBalance")

    if response.get("errorId", 0) != 0:
        logger.error(f"Не удалось проверить баланс Anti-Captcha: {response.get('errorDescription')}")
        return False # Ошибка при проверке баланса

    balance = response.get("balance")
    if balance is not None:
        logger.info(f"Текущий баланс Anti-Captcha: ${balance}")
        if float(balance) < 0.1: # Порог предупреждения
            logger.warning(f"НИЗКИЙ БАЛАНС Anti-Captcha: ${balance}. Рекомендуется пополнить.")
        return True
    else:
        logger.error("Не удалось получить значение баланса из ответа Anti-Captcha.")
        return False

async def select_category(page, category_name="Individuals"):
    """Выбирает категорию на странице"""
    logger.info(f"Выбираем категорию: {category_name}")

    # ДЕТАЛЬНАЯ ДИАГНОСТИКА ВСЕХ ГАЛОЧЕК
    logger.info("🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ВСЕХ ГАЛОЧЕК НА СТРАНИЦЕ...")

    all_checkboxes_info = await page.evaluate("""
        () => {
            const checkboxes = [];
            const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');

            allCheckboxes.forEach((checkbox, index) => {
                const label = checkbox.closest('label') ||
                             document.querySelector(`label[for="${checkbox.id}"]`) ||
                             checkbox.nextElementSibling ||
                             checkbox.previousElementSibling;

                const labelText = label ? label.textContent.trim() : '';
                const parentText = checkbox.parentElement ? checkbox.parentElement.textContent.trim() : '';

                checkboxes.push({
                    index: index,
                    id: checkbox.id || 'NO_ID',
                    name: checkbox.name || 'NO_NAME',
                    value: checkbox.value || 'NO_VALUE',
                    checked: checkbox.checked,
                    labelText: labelText,
                    parentText: parentText,
                    visible: checkbox.offsetParent !== null
                });
            });

            return checkboxes;
        }
    """)

    logger.info(f"📊 Найдено {len(all_checkboxes_info)} галочек на странице:")
    for i, cb in enumerate(all_checkboxes_info):
        logger.info(f"  Галочка {i}: id={cb['id']}, name={cb['name']}, label='{cb['labelText']}', checked={cb['checked']}")

    # Сначала снимаем все чекбоксы
    await page.evaluate("""
        () => {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('Снят чекбокс:', checkbox.id || checkbox.name);
                }
            });
        }
    """)

    await page.wait_for_timeout(2000)

    # ТОЧНЫЙ ВЫБОР ТОЛЬКО НУЖНЫХ ГАЛОЧЕК
    logger.info("🎯 ТОЧНЫЙ ВЫБОР ТОЛЬКО ГАЛОЧКИ 'Individuals'...")

    category_selected = await page.evaluate("""
        () => {
            // ТОЧНО выбираем только галочку "Individuals" по ID
            const individualsCheckbox = document.getElementById('ListeClienteleDesserviesLeftColumn_0__Selected');
            if (individualsCheckbox) {
                individualsCheckbox.checked = true;
                individualsCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
                console.log('✅ ТОЧНО отмечена галочка Individuals:', individualsCheckbox.id);
                return true;
            } else {
                console.error('❌ Галочка Individuals не найдена по точному ID');
                return false;
            }
        }
    """)

    if category_selected:
        # ТОЧНЫЙ ВЫБОР ГАЛОЧКИ "Accepting new clients"
        logger.info("🎯 ТОЧНЫЙ ВЫБОР ТОЛЬКО ГАЛОЧКИ 'Accepting new clients'...")

        accepting_clients_selected = await page.evaluate("""
            () => {
                // ТОЧНО выбираем только галочку "Accepting new clients" по ID
                const acceptingClientsCheckbox = document.getElementById('acceptClients');
                if (acceptingClientsCheckbox) {
                    acceptingClientsCheckbox.checked = true;
                    acceptingClientsCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('✅ ТОЧНО отмечена галочка Accepting new clients:', acceptingClientsCheckbox.id);
                    return true;
                } else {
                    console.error('❌ Галочка Accepting new clients не найдена по точному ID');
                    return false;
                }
            }
        """)

        if accepting_clients_selected:
            logger.info("✅ Чекбокс 'Accepting new clients' также отмечен")
        else:
            logger.warning("⚠️ Не удалось отметить чекбокс 'Accepting new clients'")

        # ПРОВЕРЯЕМ, что выбраны ТОЛЬКО нужные галочки
        logger.info("🔍 ПРОВЕРЯЕМ ФИНАЛЬНОЕ СОСТОЯНИЕ ГАЛОЧЕК...")

        final_checkboxes_state = await page.evaluate("""
            () => {
                const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
                const checkedBoxes = [];
                const uncheckedBoxes = [];

                allCheckboxes.forEach(checkbox => {
                    const label = checkbox.closest('label') ||
                                 document.querySelector(`label[for="${checkbox.id}"]`) ||
                                 checkbox.nextElementSibling ||
                                 checkbox.previousElementSibling;

                    const labelText = label ? label.textContent.trim() : '';

                    const info = {
                        id: checkbox.id,
                        name: checkbox.name,
                        labelText: labelText,
                        checked: checkbox.checked
                    };

                    if (checkbox.checked) {
                        checkedBoxes.push(info);
                    } else {
                        uncheckedBoxes.push(info);
                    }
                });

                return { checkedBoxes, uncheckedBoxes, totalChecked: checkedBoxes.length };
            }
        """)

        logger.info(f"📊 ФИНАЛЬНОЕ СОСТОЯНИЕ: {final_checkboxes_state['totalChecked']} галочек выбрано")
        logger.info("✅ ВЫБРАННЫЕ галочки:")
        for cb in final_checkboxes_state['checkedBoxes']:
            logger.info(f"   - {cb['id']}: '{cb['labelText']}'")

        # Проверяем, что выбраны ТОЛЬКО правильные галочки
        expected_checked = ['ListeClienteleDesserviesLeftColumn_0__Selected', 'acceptClients']
        actual_checked = [cb['id'] for cb in final_checkboxes_state['checkedBoxes']]

        if set(actual_checked) == set(expected_checked):
            logger.info("🎯 ✅ ОТЛИЧНО! Выбраны ТОЛЬКО правильные галочки!")
        else:
            logger.warning(f"⚠️ ПРОБЛЕМА! Ожидались: {expected_checked}, но выбраны: {actual_checked}")

            # Исправляем лишние галочки
            logger.info("🔧 ИСПРАВЛЯЕМ лишние галочки...")
            await page.evaluate("""
                () => {
                    const expectedIds = ['ListeClienteleDesserviesLeftColumn_0__Selected', 'acceptClients'];
                    const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');

                    allCheckboxes.forEach(checkbox => {
                        if (expectedIds.includes(checkbox.id)) {
                            // Эти должны быть отмечены
                            if (!checkbox.checked) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('✅ Отмечена нужная галочка:', checkbox.id);
                            }
                        } else {
                            // Все остальные должны быть сняты
                            if (checkbox.checked) {
                                checkbox.checked = false;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('❌ Снята лишняя галочка:', checkbox.id);
                            }
                        }
                    });
                }
            """)

        # КРИТИЧЕСКИ ВАЖНО: Заполняем текстовые поля!
        # URL содержит Length=8, возможно требуется минимум 8 символов
        logger.info("🔍 ЗАПОЛНЯЕМ ТЕКСТОВЫЕ ПОЛЯ (возможно, обязательные)...")

        fields_filled = await page.evaluate("""
            () => {
                let filledCount = 0;

                // ДИАГНОСТИКА: Найдем все текстовые поля на странице
                console.log('🔍 ДИАГНОСТИКА ВСЕХ ТЕКСТОВЫХ ПОЛЕЙ НА СТРАНИЦЕ:');
                const allTextInputs = document.querySelectorAll('input[type="text"]');
                allTextInputs.forEach((input, index) => {
                    console.log(`Поле ${index + 1}:`, {
                        name: input.name || 'NO_NAME',
                        id: input.id || 'NO_ID',
                        placeholder: input.placeholder || 'NO_PLACEHOLDER',
                        value: input.value || 'EMPTY',
                        visible: input.offsetParent !== null
                    });
                });
                console.log(`Всего найдено текстовых полей: ${allTextInputs.length}`);

                // 1. ПРОПУСКАЕМ все текстовые поля (не влияет на результат)
                console.log('⏭️ Пропускаем заполнение всех текстовых полей - это не влияет на результат...');

                // 5. Проверяем общую длину всех заполненных полей
                const allInputs = document.querySelectorAll('input[type="text"]');
                let totalLength = 0;
                let filledFields = [];

                allInputs.forEach(input => {
                    if (input.value && input.value.trim().length > 0) {
                        totalLength += input.value.length;
                        filledFields.push({
                            name: input.name || input.id || 'unknown',
                            value: input.value,
                            length: input.value.length
                        });
                    }
                });

                console.log(`� Общая статистика заполнения:`);
                console.log(`   Заполнено полей: ${filledCount}`);
                console.log(`   Общая длина данных: ${totalLength} символов`);
                console.log(`   Детали полей:`, filledFields);

                return { filledCount, totalLength, filledFields };
            }
        """)

        if isinstance(fields_filled, dict):
            logger.info(f"✅ Заполнено {fields_filled['filledCount']} текстовых полей")
            logger.info(f"📊 Общая длина данных: {fields_filled['totalLength']} символов")
            logger.info(f"📝 Детали полей: {fields_filled['filledFields']}")

            # Проверяем соответствие требованию Length=8
            if fields_filled['totalLength'] >= 8:
                logger.info("✅ Требование Length=8 выполнено!")
            else:
                logger.warning(f"⚠️ Общая длина данных ({fields_filled['totalLength']}) меньше требуемых 8 символов")
        elif fields_filled > 0:
            logger.info(f"✅ Заполнено {fields_filled} текстовых полей")
        else:
            logger.warning("⚠️ Не удалось заполнить текстовые поля - возможно, они не найдены")

    if category_selected:
        logger.info(f"Категория '{category_name}' успешно выбрана")
        await page.wait_for_timeout(3000)  # Ждем обновления формы
        return True
    else:
        logger.error(f"Не удалось выбрать категорию '{category_name}'")
        return False

async def solve_captcha_simple(page):
    """Простое решение капчи через Anti-Captcha с ужесточенными проверками"""
    if not ANTICAPTCHA_KEY:
        logger.warning("ANTICAPTCHA_API_KEY не установлен, используем ручное решение")
        input("Решите капчу вручную и нажмите Enter для продолжения...")
        return True

    # Проверка баланса перед началом
    if not await check_anticaptcha_balance():
        logger.error("Проверка баланса Anti-Captcha не удалась или баланс слишком низкий. Решение капчи прервано.")
        return False

    # Механизм повторных попыток
    max_retries = 3
    for attempt_num in range(max_retries): # Изменено имя переменной
        logger.info(f"Попытка решения капчи {attempt_num + 1}/{max_retries}...")
        try:
            # Сначала проверяем, есть ли капча на странице
            captcha_present = await page.evaluate("""
                () => {
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                    return recaptchaDiv !== null || recaptchaIframes.length > 0;
                }
            """)

            if not captcha_present:
                logger.info("Капча не найдена на странице")
                return True

            logger.info("Капча обнаружена на странице")

            # Очищаем любые существующие токены
            await page.evaluate("""
                () => {
                    const existingToken = document.querySelector('#g-recaptcha-response');
                    if (existingToken) {
                        existingToken.value = '';
                        console.log('Очищен существующий токен');
                    }
                }
            """)

            existing_token = await page.evaluate("""
                () => {
                    const response = document.querySelector('#g-recaptcha-response');
                    return response ? response.value : null;
                }
            """)

            if existing_token and len(existing_token) > 50:
                logger.warning(f"ВНИМАНИЕ: Найден существующий токен длиной {len(existing_token)}! Возможно кэш.")
                logger.info("Принудительно очищаем токен...")
                await page.evaluate("""
                    () => {
                        const response = document.querySelector('#g-recaptcha-response');
                        if (response) {
                            response.remove();
                        }
                        const hiddenFields = document.querySelectorAll('input[name="g-recaptcha-response"]');
                        hiddenFields.forEach(field => field.remove());
                    }
                """)

            sitekey = await page.evaluate("""
                () => {
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                        return recaptchaDiv.getAttribute('data-sitekey');
                    }
                    const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                    for (const iframe of iframes) {
                        const src = iframe.src;
                        const match = src.match(/k=([^&]+)/);
                        if (match) {
                            return match[1];
                        }
                    }
                    return null;
                }
            """)

            if not sitekey:
                logger.error("Не удалось найти sitekey, переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            logger.info(f"Найден sitekey: {sitekey[:10]}...")

            checkbox_state = await page.evaluate("""
                () => {
                    const iframes = document.querySelectorAll('iframe[src*="api2/anchor"]');
                    if (iframes.length > 0) {
                        try {
                            const iframe = iframes[0];
                            const frame = iframe.contentDocument || iframe.contentWindow.document;
                            const checkbox = frame.querySelector('#recaptcha-anchor');
                            return checkbox ? checkbox.getAttribute('aria-checked') : 'unknown';
                        } catch (e) {
                            return 'cross-origin';
                        }
                    }
                    return 'no-iframe';
                }
            """)
            logger.info(f"Состояние чекбокса reCAPTCHA: {checkbox_state}")

            logger.info("Отправляем задачу в Anti-Captcha...")
            task_payload = {
                "task": {
                    "type": "NoCaptchaTaskProxyless",
                    "websiteURL": BASE_URL,
                    "websiteKey": sitekey,
                }
            }

            response_create_task = await anticaptcha_request("createTask", **task_payload)
            if response_create_task.get("errorId", 0) != 0:
                logger.error(f"Ошибка создания задачи Anti-Captcha: {response_create_task.get('errorDescription')}, переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            task_id = response_create_task.get("taskId")
            if not task_id:
                logger.error("Не удалось создать задачу Anti-Captcha (нет taskId в ответе), переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            logger.info(f"Задача Anti-Captcha создана, ID: {task_id}")

            start_time_task = asyncio.get_event_loop().time() # Используем другое имя переменной
            await asyncio.sleep(10)

            task_solved_successfully = False
            for poll_attempt in range(50): # Изменено имя переменной
                current_time = asyncio.get_event_loop().time()
                elapsed = current_time - start_time_task

                response_get_result = await anticaptcha_request("getTaskResult", taskId=task_id)

                if response_get_result.get("errorId", 0) != 0:
                    logger.error(f"Ошибка получения результата задачи Anti-Captcha: {response_get_result.get('errorDescription')}")
                    if response_get_result.get("errorCode") == "ERROR_KEY_DOES_NOT_EXIST":
                        logger.critical("Ключ Anti-Captcha не существует. Прерывание всех попыток.")
                        return False # Прерываем все
                    # Для других ошибок просто ждем и пробуем еще раз получить результат
                    await asyncio.sleep(5)
                    continue # К следующей попытке получения результата

                if response_get_result.get("status") == "ready":
                    solution = response_get_result.get("solution")
                    token = solution.get("gRecaptchaResponse") if solution else None

                    if token and len(token) > 50:
                        logger.info(f"Получен токен от Anti-Captcha за {elapsed:.1f}с, длина: {len(token)}")
                        if not token.startswith("03"): # Эта проверка может быть не всегда актуальна
                             logger.warning(f"Токен имеет формат, отличный от ожидаемого (не начинается с '03'): {token[:20]}...")

                        # Вставляем токен и делаем кнопки активными
                        await page.evaluate(f"""
                            (token) => {{
                                let response_el = document.querySelector('#g-recaptcha-response');
                                if (!response_el) {{
                                    response_el = document.createElement('textarea');
                                    response_el.id = 'g-recaptcha-response';
                                    response_el.name = 'g-recaptcha-response';
                                    response_el.style.display = 'none';
                                    document.body.appendChild(response_el);
                                }}
                                response_el.value = token;
                                response_el.dispatchEvent(new Event('change', {{ bubbles: true }}));

                                // Принудительно активируем все кнопки отправки
                                const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                                buttons.forEach(btn => {{
                                    btn.disabled = false;
                                    btn.removeAttribute('disabled');
                                    // Применяем стили, как будто кнопка активна
                                    btn.classList.remove('disabled');
                                    btn.style.opacity = '1';
                                    btn.style.cursor = 'pointer';
                                    console.log('Активирована кнопка:', btn);
                                }});

                                // Если форма содержит reCAPTCHA, отмечаем ее как решенную
                                const recaptchaDiv = document.querySelector('.g-recaptcha');
                                if (recaptchaDiv) {{
                                    recaptchaDiv.dataset.captchaResolved = 'true';
                                }}

                                // Удаляем любые блокировки для формы, если они есть
                                const forms = document.querySelectorAll('form');
                                forms.forEach(form => {{
                                    if (form.getAttribute('data-captcha-required') === 'true') {{
                                        form.setAttribute('data-captcha-required', 'false');
                                    }}
                                }});

                                console.log('Новый токен капчи вставлен, длина:', token.length);
                            }}
                        """, token)

                        await page.wait_for_timeout(2000)
                        inserted_token = await page.evaluate("""
                            () => {
                                const response = document.querySelector('#g-recaptcha-response');
                                return response ? response.value : null;
                            }
                        """)

                        if inserted_token and len(inserted_token) > 50:
                            logger.info(f"Токен успешно вставлен и проверен, длина: {len(inserted_token)}")

                            # Проверяем статус кнопок после вставки токена
                            buttons_state = await page.evaluate("""
                                () => {
                                    const buttons = Array.from(document.querySelectorAll('button[type="submit"], input[type="submit"]'));
                                    return buttons.map(btn => ({
                                        text: btn.textContent || btn.value || 'КНОПКА',
                                        disabled: btn.disabled,
                                        visible: btn.offsetParent !== null
                                    }));
                                }
                            """)
                            logger.info(f"Состояние кнопок после вставки токена: {buttons_state}")

                            # Если токен вставлен, считаем капчу решенной
                            logger.info("КАПЧА РЕШЕНА! Токен вставлен, кнопки активированы.")
                            task_solved_successfully = True
                            break # Успешное решение, выход из цикла ожидания результата
                        else:
                            logger.error("Токен не найден после вставки! Переход к следующей основной попытке...")
                            await asyncio.sleep(2)
                            break # Выход из цикла ожидания результата, переход к следующей основной попытке

                elif response_get_result.get("status") == "processing":
                    logger.debug(f"Задача {task_id} обрабатывается... (попытка {poll_attempt + 1}, время: {elapsed:.1f}с)")
                else:
                    logger.warning(f"Неожиданный статус от Anti-Captcha: {response_get_result.get('status')}")

                await asyncio.sleep(5)

            if task_solved_successfully:
                return True # Капча успешно решена в этой основной попытке
            else:
                logger.error(f"Не удалось решить задачу {task_id} на попытке {attempt_num + 1} (таймаут или ошибка вставки токена)")
                # Продолжаем к следующей основной попытке, если есть
                await asyncio.sleep(2)
                continue # К следующей основной попытке

        except Exception as e_main_try:
            logger.error(f"Ошибка в основной попытке {attempt_num + 1} решения капчи: {e_main_try}", exc_info=True)
            if attempt_num < max_retries - 1:
                logger.info("Пауза перед следующей попыткой...")
                await asyncio.sleep(5)
            # Если это последняя попытка, цикл завершится и функция вернет False ниже

    logger.error("Все попытки решения капчи исчерпаны.")
    return False

async def emulate_human_behavior(page):
    """Эмулирует человеческое поведение перед решением капчи"""
    try:
        logger.info("🤖 Эмулируем человеческое поведение...")

        # 1. Случайные движения мыши по странице
        logger.info("🖱️ Эмулируем движения мыши...")

        # Получаем размеры viewport
        viewport = await page.evaluate("() => ({ width: window.innerWidth, height: window.innerHeight })")

        # Делаем 3-5 случайных движений мыши
        for i in range(random.randint(3, 5)):
            x = random.randint(100, viewport['width'] - 100)
            y = random.randint(100, viewport['height'] - 100)

            # Плавное движение мыши
            await page.mouse.move(x, y)
            await page.wait_for_timeout(random.randint(200, 800))

        # 2. Скроллинг страницы (имитация чтения)
        logger.info("📜 Эмулируем скроллинг...")
        await page.evaluate("""
            () => {
                window.scrollTo({ top: 200, behavior: 'smooth' });
            }
        """)
        await page.wait_for_timeout(random.randint(1000, 2000))

        await page.evaluate("""
            () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        """)
        await page.wait_for_timeout(random.randint(500, 1000))

        # 3. Фокус ТОЛЬКО на текстовых элементах (НЕ на галочках!)
        logger.info("🎯 Эмулируем взаимодействие с текстовыми полями...")

        # Кликаем ТОЛЬКО на текстовые поля, НЕ на галочки!
        text_elements = await page.query_selector_all('input[type="text"]')
        if text_elements:
            # Кликаем на 1-2 случайных ТЕКСТОВЫХ элемента
            for _ in range(min(2, len(text_elements))):
                element = random.choice(text_elements)
                try:
                    await element.click()
                    await page.wait_for_timeout(random.randint(300, 700))
                    logger.info("🖱️ Клик по текстовому полю (эмуляция)")
                except:
                    pass

        # 4. Наведение на капчу и пауза (важно!)
        logger.info("🔍 Наводим мышь на капчу...")
        captcha_div = await page.query_selector('.g-recaptcha')
        if captcha_div:
            # Получаем координаты капчи
            box = await captcha_div.bounding_box()
            if box:
                # Наводим мышь на центр капчи
                center_x = box['x'] + box['width'] / 2
                center_y = box['y'] + box['height'] / 2

                await page.mouse.move(center_x, center_y)
                await page.wait_for_timeout(random.randint(1000, 2000))

                # Небольшие движения вокруг капчи
                for _ in range(2):
                    offset_x = random.randint(-20, 20)
                    offset_y = random.randint(-20, 20)
                    await page.mouse.move(center_x + offset_x, center_y + offset_y)
                    await page.wait_for_timeout(random.randint(200, 500))

        # 5. Пауза перед решением (важно для анализа поведения)
        pause_time = random.randint(2000, 4000)
        logger.info(f"⏳ Пауза перед решением капчи: {pause_time/1000:.1f}с")
        await page.wait_for_timeout(pause_time)

        logger.info("✅ Эмуляция человеческого поведения завершена")
        return True

    except Exception as e:
        logger.warning(f"⚠️ Ошибка при эмуляции поведения: {e}")
        return True  # Продолжаем даже если эмуляция не удалась

async def solve_captcha_with_2captcha(page, captcha_info):
    """Решает капчу с использованием 2captcha.com и эмуляцией человеческого поведения"""
    try:
        logger.info("🔍 Начинаем решение капчи с 2captcha.com и эмуляцией человеческого поведения...")

        # СНАЧАЛА эмулируем человеческое поведение
        await emulate_human_behavior(page)

        # Проверяем баланс 2captcha
        session = await get_twocaptcha_session()

        logger.info("💰 Проверяем баланс 2captcha...")
        async with session.get(
            "http://2captcha.com/res.php",
            params={"key": TWOCAPTCHA_KEY, "action": "getbalance"}
        ) as response:
            balance_text = await response.text()

        if balance_text.startswith("ERROR"):
            logger.error(f"❌ Ошибка проверки баланса 2captcha: {balance_text}")
            return False
        else:
            balance = float(balance_text)
            logger.info(f"Текущий баланс 2captcha: ${balance}")
            if balance < 0.002:
                logger.error("❌ Недостаточно средств на балансе 2captcha")
                return False

        # Получаем реальные cookies из браузера
        logger.info("🍪 Получаем cookies из браузера...")
        browser_cookies = await page.context.cookies()
        cookies_string = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in browser_cookies])
        logger.info(f"Получено {len(browser_cookies)} cookies")

        # Получаем реальный User-Agent из браузера
        user_agent = await page.evaluate("() => navigator.userAgent")
        logger.info(f"User-Agent: {user_agent}")

        # Подготавливаем параметры для 2captcha
        submit_params = {
            "key": TWOCAPTCHA_KEY,
            "method": "userrecaptcha",
            "googlekey": captcha_info['divSitekey'],
            "pageurl": page.url,
            "useragent": user_agent,
            "cookies": cookies_string,
            "json": "1"  # Получаем ответ в JSON формате
        }

        logger.info(f"Параметры задачи 2captcha: googlekey={captcha_info['divSitekey'][:10]}..., pageurl={page.url}")
        logger.info(f"Cookies: {len(cookies_string)} символов")
        logger.info(f"URL: {page.url}")

        # Отправляем задачу в 2captcha
        logger.info("📤 Отправляем задачу в 2captcha...")
        async with session.post(
            "http://2captcha.com/in.php",
            data=submit_params
        ) as response:
            submit_result = await response.json()

        if submit_result.get("status") != 1:
            logger.error(f"❌ Ошибка создания задачи 2captcha: {submit_result}")
            return False

        captcha_id = submit_result.get("request")
        logger.info(f"✅ Задача 2captcha создана успешно! ID: {captcha_id}")

        # Ждем решения
        start_time = time.time()
        max_wait_time = 120  # 2 минуты

        while time.time() - start_time < max_wait_time:
            await asyncio.sleep(5)  # 2captcha рекомендует проверять каждые 5 секунд

            # Проверяем результат
            async with session.get(
                "http://2captcha.com/res.php",
                params={"key": TWOCAPTCHA_KEY, "action": "get", "id": captcha_id, "json": "1"}
            ) as response:
                result = await response.json()

            if result.get("status") == 1:
                token = result.get("request")
                elapsed_time = time.time() - start_time
                logger.info(f"✅ Получен токен от 2captcha за {elapsed_time:.1f}с, длина: {len(token)}")
                logger.info(f"🎯 Сервис: 2captcha.com")
                logger.info(f"🍪 Использованы cookies: {len(cookies_string)} символов")
                logger.info(f"🌐 User-Agent: {user_agent[:50]}...")

                # УЛУЧШЕННАЯ ВСТАВКА ТОКЕНА с эмуляцией
                success = await insert_captcha_token_enhanced(page, token, captcha_info)
                return success

            elif result.get("request") == "CAPCHA_NOT_READY":
                elapsed = time.time() - start_time
                logger.info(f"Капча решается... ({elapsed:.1f}с)")
            else:
                logger.warning(f"Неожиданный ответ от 2captcha: {result}")

        logger.error("Превышено время ожидания решения капчи")
        return False

    except Exception as e:
        logger.error(f"Ошибка при решении капчи через 2captcha: {e}")
        return False

async def solve_captcha_enhanced(page, captcha_info):
    """Решает капчу с эмуляцией человеческого поведения (Anti-Captcha fallback)"""
    try:
        logger.info("🔍 Начинаем решение капчи с эмуляцией человеческого поведения...")

        # СНАЧАЛА эмулируем человеческое поведение
        await emulate_human_behavior(page)

        # Проверяем баланс Anti-Captcha
        balance_response = await anticaptcha_session.post(
            "https://api.anti-captcha.com/getBalance",
            json={"clientKey": ANTICAPTCHA_KEY}
        )
        balance_data = await balance_response.json()

        if balance_data.get("errorId") == 0:
            balance = balance_data.get("balance", 0)
            logger.info(f"Текущий баланс Anti-Captcha: ${balance}")
            if balance < 0.1:
                logger.error("Недостаточно средств на балансе Anti-Captcha")
                return False
        else:
            logger.warning(f"Не удалось проверить баланс: {balance_data}")

        # Получаем реальные cookies из браузера
        logger.info("🍪 Получаем cookies из браузера...")
        browser_cookies = await page.context.cookies()
        cookies_string = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in browser_cookies])
        logger.info(f"Получено {len(browser_cookies)} cookies")

        # Получаем реальный User-Agent из браузера
        user_agent = await page.evaluate("() => navigator.userAgent")
        logger.info(f"User-Agent: {user_agent}")

        # Подготавливаем параметры для решения капчи с МАКСИМАЛЬНО ТОЧНЫМИ настройками
        task_data = {
            "clientKey": ANTICAPTCHA_KEY,
            "task": {
                "type": "RecaptchaV2TaskProxyless",  # Возвращаемся к RecaptchaV2TaskProxyless
                "websiteURL": page.url,  # Используем точный текущий URL
                "websiteKey": captcha_info['divSitekey'],
                "isInvisible": captcha_info['divSize'] == 'invisible',
                "userAgent": user_agent,  # Реальный User-Agent из браузера
                "cookies": cookies_string,  # Реальные cookies из браузера
                "pageAction": captcha_info.get('divAction', ''),  # Добавляем action если есть
                "isEnterprise": False  # Указываем, что это не Enterprise версия
            }
        }

        # Добавляем дополнительные параметры если они есть
        if captcha_info['divAction']:
            task_data["task"]["recaptchaDataSValue"] = captcha_info['divAction']

        logger.info(f"Параметры задачи: type={task_data['task']['type']}, "
                   f"invisible={task_data['task']['isInvisible']}, "
                   f"action={captcha_info['divAction']}")
        logger.info(f"Cookies: {len(cookies_string)} символов")
        logger.info(f"URL: {task_data['task']['websiteURL']}")

        # Список типов задач для попытки (в порядке приоритета)
        task_types_to_try = [
            "RecaptchaV2TaskProxyless",
            "NoCaptchaTaskProxyless",
            "RecaptchaV2Task"  # Если проксилесс не работает
        ]

        task_id = None
        successful_task_type = None

        # Пробуем разные типы задач
        for task_type in task_types_to_try:
            logger.info(f"🔄 Пробуем тип задачи: {task_type}")

            # Обновляем тип задачи
            current_task_data = task_data.copy()
            current_task_data["task"]["type"] = task_type

            # Отправляем задачу
            logger.info("Отправляем задачу в Anti-Captcha...")
            create_response = await anticaptcha_session.post(
                "https://api.anti-captcha.com/createTask",
                json=current_task_data
            )
            create_data = await create_response.json()

            if create_data.get("errorId") == 0:
                task_id = create_data["taskId"]
                successful_task_type = task_type
                logger.info(f"✅ Задача Anti-Captcha создана успешно! ID: {task_id}, тип: {task_type}")
                break
            else:
                logger.warning(f"❌ Ошибка с типом {task_type}: {create_data}")
                continue

        if not task_id:
            logger.error("❌ Не удалось создать задачу ни с одним типом!")
            return False

        # Ждем решения
        start_time = time.time()
        max_wait_time = 120  # 2 минуты

        while time.time() - start_time < max_wait_time:
            await asyncio.sleep(3)

            result_response = await anticaptcha_session.post(
                "https://api.anti-captcha.com/getTaskResult",
                json={"clientKey": ANTICAPTCHA_KEY, "taskId": task_id}
            )
            result_data = await result_response.json()

            if result_data.get("errorId") != 0:
                logger.error(f"Ошибка получения результата: {result_data}")
                return False

            if result_data.get("status") == "ready":
                token = result_data["solution"]["gRecaptchaResponse"]
                elapsed_time = time.time() - start_time
                logger.info(f"✅ Получен токен от Anti-Captcha за {elapsed_time:.1f}с, длина: {len(token)}")
                logger.info(f"🎯 Успешный тип задачи: {successful_task_type}")
                logger.info(f"🍪 Использованы cookies: {len(cookies_string)} символов")
                logger.info(f"🌐 User-Agent: {user_agent[:50]}...")

                # УЛУЧШЕННАЯ ВСТАВКА ТОКЕНА
                success = await insert_captcha_token_enhanced(page, token, captcha_info)
                return success

            elif result_data.get("status") == "processing":
                elapsed = time.time() - start_time
                logger.info(f"Капча решается... ({elapsed:.1f}с)")
            else:
                logger.warning(f"Неожиданный статус: {result_data.get('status')}")

        logger.error("Превышено время ожидания решения капчи")
        return False

    except Exception as e:
        logger.error(f"Ошибка при решении капчи: {e}")
        return False

async def insert_captcha_token_enhanced(page, token, captcha_info):
    """Улучшенная вставка токена капчи с эмуляцией человеческого взаимодействия"""
    try:
        logger.info("🔧 Вставляем токен капчи с эмуляцией человеческого взаимодействия...")

        # СНАЧАЛА эмулируем клик по чекбоксу капчи (если он есть)
        logger.info("🖱️ Эмулируем клик по чекбоксу капчи...")

        # Ищем чекбокс капчи
        captcha_checkbox = await page.query_selector('.g-recaptcha iframe')
        if captcha_checkbox:
            try:
                # Наводим мышь на чекбокс
                await captcha_checkbox.hover()
                await page.wait_for_timeout(500)

                # Кликаем по чекбоксу
                await captcha_checkbox.click()
                logger.info("✅ Клик по чекбоксу капчи выполнен")
                await page.wait_for_timeout(1000)
            except Exception as e:
                logger.warning(f"⚠️ Не удалось кликнуть по чекбоксу капчи: {e}")

        # Эмулируем задержку "размышления"
        thinking_time = random.randint(1000, 3000)
        logger.info(f"🤔 Эмулируем 'размышление' пользователя: {thinking_time/1000:.1f}с")
        await page.wait_for_timeout(thinking_time)

        result = await page.evaluate("""
            (args) => {
                const token = args.token;
                const captchaInfo = args.captchaInfo;
                console.log('🔧 Начинаем улучшенную вставку токена с эмуляцией...');

                // 1. Вставляем токен в поле ответа
                const responseField = document.querySelector('#g-recaptcha-response');
                if (!responseField) {
                    console.error('❌ Поле g-recaptcha-response не найдено!');
                    return { success: false, reason: 'Response field not found' };
                }

                responseField.value = token;
                console.log('✅ Токен вставлен в поле, длина:', token.length);

                // 2. Эмулируем "человеческую" активацию капчи
                console.log('🤖 Эмулируем человеческую активацию капчи...');

                // Триггерим события изменения с задержками
                setTimeout(() => {
                    responseField.dispatchEvent(new Event('input', { bubbles: true }));
                }, 100);

                setTimeout(() => {
                    responseField.dispatchEvent(new Event('change', { bubbles: true }));
                }, 200);

                setTimeout(() => {
                    responseField.dispatchEvent(new Event('blur', { bubbles: true }));
                }, 300);

                console.log('✅ События input/change/blur отправлены с задержками');

                // 3. Пытаемся активировать grecaptcha API более "человечно"
                if (window.grecaptcha) {
                    try {
                        console.log('🔄 Активируем grecaptcha API...');

                        // Пытаемся найти widget ID
                        const recaptchaDiv = document.querySelector('.g-recaptcha');
                        if (recaptchaDiv && window.grecaptcha.render) {
                            console.log('🔄 Пытаемся активировать widget...');
                        }

                        // Проверяем текущий ответ
                        if (window.grecaptcha.getResponse) {
                            const currentResponse = window.grecaptcha.getResponse();
                            console.log('Текущий ответ grecaptcha:', currentResponse ? currentResponse.length : 'null');
                        }

                        // Пытаемся вызвать callback если он есть
                        if (captchaInfo.divCallback && window[captchaInfo.divCallback]) {
                            console.log('🔄 Вызываем callback функцию:', captchaInfo.divCallback);
                            setTimeout(() => {
                                try {
                                    window[captchaInfo.divCallback](token);
                                    console.log('✅ Callback функция вызвана');
                                } catch (e) {
                                    console.warn('⚠️ Ошибка при вызове callback:', e);
                                }
                            }, 500);
                        }

                    } catch (e) {
                        console.warn('⚠️ Ошибка при работе с grecaptcha API:', e);
                    }
                }

                // 4. Эмулируем изменение состояния капчи
                console.log('🔄 Эмулируем изменение состояния капчи...');

                // Добавляем класс "решено" к div капчи
                const recaptchaDiv = document.querySelector('.g-recaptcha');
                if (recaptchaDiv) {
                    recaptchaDiv.classList.add('recaptcha-solved');
                    recaptchaDiv.setAttribute('data-solved', 'true');
                }

                // 5. Проверяем состояние кнопок после вставки токена
                const buttons = document.querySelectorAll('button, input[type="submit"]');
                const buttonStates = Array.from(buttons).map(btn => ({
                    text: btn.textContent || btn.value || 'NO_TEXT',
                    disabled: btn.disabled,
                    visible: btn.offsetParent !== null
                }));

                console.log('📊 Состояние кнопок после вставки токена:', buttonStates);

                // 6. Финальная проверка
                const finalCheck = {
                    tokenInField: responseField.value.length > 0,
                    tokenLength: responseField.value.length,
                    fieldVisible: responseField.offsetParent !== null,
                    callbackExists: !!(captchaInfo.divCallback && window[captchaInfo.divCallback]),
                    grecaptchaExists: !!window.grecaptcha,
                    divHasSolvedClass: recaptchaDiv ? recaptchaDiv.classList.contains('recaptcha-solved') : false
                };

                console.log('🔍 Финальная проверка:', finalCheck);

                return {
                    success: true,
                    tokenLength: token.length,
                    buttonStates: buttonStates,
                    finalCheck: finalCheck
                };
            }
        """, {"token": token, "captchaInfo": captcha_info})

        # Дополнительная пауза после вставки токена
        post_insert_delay = random.randint(1000, 2000)
        logger.info(f"⏳ Пауза после вставки токена: {post_insert_delay/1000:.1f}с")
        await page.wait_for_timeout(post_insert_delay)

        if result['success']:
            logger.info(f"✅ Токен успешно вставлен и обработан, длина: {result['tokenLength']}")
            logger.info(f"Состояние кнопок: {result['buttonStates']}")
            logger.info(f"Финальная проверка: {result['finalCheck']}")
            return True
        else:
            logger.error(f"❌ Ошибка при вставке токена: {result.get('reason', 'Unknown error')}")
            return False

    except Exception as e:
        logger.error(f"Ошибка при вставке токена: {e}")
        return False

async def analyze_results_after_navigation(page):
    """Анализирует результаты после навигации"""
    try:
        logger.info("🔍 Анализируем результаты после навигации...")

        # Ждем полной загрузки страницы
        await page.wait_for_load_state("domcontentloaded")
        await page.wait_for_timeout(3000)

        # Получаем информацию о результатах
        current_url = page.url
        logger.info(f"URL после навигации: {current_url}")

        # Анализируем содержимое страницы
        results_info = await page.evaluate("""
            () => {
                const pageText = document.body.innerText;
                const url = window.location.href;

                // Проверяем, находимся ли мы на странице результатов
                const isResultsPage = url.includes('/search/') ||
                                    url.includes('/results/') ||
                                    url.includes('FindACPABottinFormSubmit');

                const isFormPage = pageText.includes('Search criteria') ||
                                 pageText.includes('Last name of CPA') ||
                                 url.includes('/find-a-cpa/cpa-directory/');

                // Ищем результаты CPA
                const resultMatches = pageText.match(/(\\d+)\\s*-\\s*(\\d+)\\s*of\\s*(\\d+)\\s*result/i);
                const emailElements = document.querySelectorAll('a[href^="mailto"]');
                const phoneElements = document.querySelectorAll('a[href^="tel"]');

                return {
                    url: url,
                    isFormPage: isFormPage,
                    isResultsPage: isResultsPage,
                    pageText: pageText.substring(0, 1000),
                    resultMatches: resultMatches,
                    emailCount: emailElements.length,
                    phoneCount: phoneElements.length,
                    pageTitle: document.title
                };
            }
        """)

        logger.info(f"Результаты анализа: {results_info}")

        if results_info['isResultsPage'] and not results_info['isFormPage']:
            logger.info("✅ УСПЕХ! Мы на странице результатов!")
            if results_info['emailCount'] > 0 or results_info['phoneCount'] > 0:
                logger.info(f"📧 Найдено контактов: email={results_info['emailCount']}, phone={results_info['phoneCount']}")
            else:
                logger.info("ℹ️ Страница результатов найдена, но контакты не обнаружены")
        else:
            logger.warning("⚠️ Похоже, мы не на странице результатов")

        # Показываем часть текста для диагностики
        logger.info("📄 Текст страницы (первые 1000 символов):")
        logger.info(results_info['pageText'])

    except Exception as e:
        logger.error(f"Ошибка при анализе результатов: {e}")

async def run_test_parser():
    """Запускает тестовый парсер с выбором категории Individuals"""
    global anticaptcha_session, twocaptcha_session

    # Инициализируем сессии для сервисов капчи
    anticaptcha_session = aiohttp.ClientSession()
    twocaptcha_session = aiohttp.ClientSession()
    logger.info("✅ Сессии Anti-Captcha и 2captcha инициализированы")

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(viewport={"width": 1280, "height": 800})
        page = await context.new_page()

        try:
            logger.info("=== Тестовый парсер CPA Quebec (категория Individuals) ===")
            logger.info(f"Переходим на {BASE_URL}")

            # Переходим на страницу
            await page.goto(BASE_URL, timeout=60000)
            await page.wait_for_load_state("domcontentloaded")

            # Закрываем cookie banner
            try:
                cookie_banner = page.locator("text=Tout refuser")
                if await cookie_banner.is_visible(timeout=5000):
                    await cookie_banner.click()
                    logger.info("Cookie banner закрыт")
                    await page.wait_for_timeout(2000)
            except:
                pass

            # Выбираем категорию "Individuals" (как изначально планировалось)
            if not await select_category(page, "Individuals"):
                logger.error("Не удалось выбрать категорию")
                return

            # Обрабатываем капчу
            # АВТОМАТИЧЕСКОЕ РЕШЕНИЕ КАПЧИ С УЛУЧШЕННЫМИ ПАРАМЕТРАМИ
            logger.info("🔍 АВТОМАТИЧЕСКОЕ РЕШЕНИЕ КАПЧИ С УЛУЧШЕННЫМИ ПАРАМЕТРАМИ...")

            # Сначала анализируем капчу на странице
            captcha_analysis = await page.evaluate("""
                () => {
                    // Ищем все элементы капчи
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                    const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                    const recaptchaScripts = document.querySelectorAll('script[src*="recaptcha"]');

                    // Проверяем глобальные переменные капчи
                    const hasGrecaptcha = typeof window.grecaptcha !== 'undefined';
                    const grecaptchaReady = hasGrecaptcha && window.grecaptcha.ready;

                    // Проверяем callback функции
                    let callbackFunction = null;
                    if (recaptchaDiv) {
                        const callbackName = recaptchaDiv.getAttribute('data-callback');
                        if (callbackName && window[callbackName]) {
                            callbackFunction = callbackName;
                        }
                    }

                    return {
                        // Элементы DOM
                        divExists: !!recaptchaDiv,
                        divSitekey: recaptchaDiv ? recaptchaDiv.getAttribute('data-sitekey') : null,
                        divCallback: recaptchaDiv ? recaptchaDiv.getAttribute('data-callback') : null,
                        divSize: recaptchaDiv ? recaptchaDiv.getAttribute('data-size') : null,
                        divTheme: recaptchaDiv ? recaptchaDiv.getAttribute('data-theme') : null,
                        divAction: recaptchaDiv ? recaptchaDiv.getAttribute('data-action') : null,

                        // Поле ответа
                        responseExists: !!recaptchaResponse,
                        responseValue: recaptchaResponse ? recaptchaResponse.value : null,
                        responseLength: recaptchaResponse ? recaptchaResponse.value.length : 0,

                        // Iframe и скрипты
                        iframeCount: recaptchaIframes.length,
                        scriptCount: recaptchaScripts.length,

                        // JavaScript API
                        hasGrecaptcha: hasGrecaptcha,
                        grecaptchaReady: grecaptchaReady,
                        callbackFunction: callbackFunction,

                        // Дополнительная информация
                        pageUrl: window.location.href,
                        userAgent: navigator.userAgent.substring(0, 100)
                    };
                }
            """)

            logger.info("=== ДЕТАЛЬНЫЙ АНАЛИЗ КАПЧИ ===")
            logger.info(f"Div капчи: {captcha_analysis['divExists']}")
            logger.info(f"Sitekey: {captcha_analysis['divSitekey']}")
            logger.info(f"Callback: {captcha_analysis['divCallback']}")
            logger.info(f"Size: {captcha_analysis['divSize']}")
            logger.info(f"Theme: {captcha_analysis['divTheme']}")
            logger.info(f"Action: {captcha_analysis['divAction']}")
            logger.info(f"Поле ответа: {captcha_analysis['responseExists']}")
            logger.info(f"Текущая длина токена: {captcha_analysis['responseLength']}")
            logger.info(f"Iframe count: {captcha_analysis['iframeCount']}")
            logger.info(f"Script count: {captcha_analysis['scriptCount']}")
            logger.info(f"grecaptcha API: {captcha_analysis['hasGrecaptcha']}")
            logger.info(f"grecaptcha.ready: {captcha_analysis['grecaptchaReady']}")
            logger.info(f"Callback function: {captcha_analysis['callbackFunction']}")

            if not captcha_analysis['divExists']:
                logger.error("❌ Div капчи не найден!")
                return

            if not captcha_analysis['divSitekey']:
                logger.error("❌ Sitekey не найден!")
                return

            # Решаем капчу с 2captcha.com
            logger.info("🚀 Решаем капчу с 2captcha.com...")
            captcha_solved = await solve_captcha_with_2captcha(page, captcha_analysis)
            if not captcha_solved:
                logger.error("❌ Не удалось решить капчу через 2captcha")
                return

            # КРИТИЧЕСКИ ВАЖНО: Подготавливаемся к возможной навигации
            logger.info("🔍 Подготавливаемся к отправке формы и возможной навигации...")

            # Запоминаем текущий URL
            initial_url = page.url
            logger.info(f"Текущий URL перед отправкой: {initial_url}")

            # Настраиваем обработчик навигации
            navigation_promise = None
            try:
                # Ждем короткое время для стабилизации
                await page.wait_for_timeout(3000)

                # Проверяем, не началась ли уже навигация
                current_url = page.url
                if current_url != initial_url:
                    logger.info(f"🚀 НАВИГАЦИЯ УЖЕ НАЧАЛАСЬ! Новый URL: {current_url}")
                    # Ждем завершения загрузки
                    await page.wait_for_load_state("domcontentloaded", timeout=30000)
                    await page.wait_for_timeout(3000)

                    # Переходим к анализу результатов
                    await analyze_results_after_navigation(page)
                    return
                else:
                    logger.info("Навигация пока не началась, продолжаем...")

            except Exception as e:
                logger.warning(f"Ошибка при проверке навигации: {e}")
                # Продолжаем выполнение

            # АНАЛИЗ ФОРМЫ
            # Логируем состояние формы перед отправкой
            form_state = await page.evaluate("""
                () => {
                    const checkboxes = Array.from(document.querySelectorAll('input[type="checkbox"]'));
                    const checkedBoxes = checkboxes.filter(cb => cb.checked);

                    return {
                        totalCheckboxes: checkboxes.length,
                        checkedCheckboxes: checkedBoxes.length,
                        checkedDetails: checkedBoxes.map(cb => ({
                            id: cb.id,
                            name: cb.name,
                            value: cb.value,
                            label: cb.closest('label') ? cb.closest('label').textContent.trim() : 'No label'
                        }))
                    };
                }
            """)
            logger.info(f"Состояние формы перед отправкой: {form_state}")

            # АНАЛИЗ ФОРМ НА СТРАНИЦЕ
            logger.info("=== АНАЛИЗ ФОРМ НА СТРАНИЦЕ ===")
            all_forms = await page.evaluate("""
                    () => {
                        const forms = Array.from(document.querySelectorAll('form'));
                        return forms.map((form, index) => {
                            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
                            const buttons = form.querySelectorAll('button, input[type="submit"], input[type="button"]');

                            return {
                                index: index,
                                id: form.id || 'NO_ID',
                                action: form.action || 'NO_ACTION',
                                method: form.method || 'NO_METHOD',
                                checkboxCount: checkboxes.length,
                                buttonCount: buttons.length
                            };
                        });
                    }
                """)

            for form_info in all_forms:
                logger.info(f"Форма #{form_info['index']}: id={form_info['id']} | "
                           f"action={form_info['action']} | checkboxes={form_info['checkboxCount']} | "
                           f"buttons={form_info['buttonCount']}")

            # ГЛУБОКИЙ АНАЛИЗ КАПЧИ И ПОИСК ПРАВИЛЬНОЙ КНОПКИ
            logger.info("🔍 АНАЛИЗИРУЕМ КАПЧУ И ИЩЕМ ПРАВИЛЬНУЮ КНОПКУ...")

            captcha_and_button_analysis = await page.evaluate("""
                () => {
                    const result = {
                        captcha: {},
                        buttons: [],
                        form: {}
                    };

                    // 1. ДЕТАЛЬНЫЙ АНАЛИЗ КАПЧИ
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                    const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]');

                    result.captcha = {
                        divExists: !!recaptchaDiv,
                        responseExists: !!recaptchaResponse,
                        responseValue: recaptchaResponse ? recaptchaResponse.value : null,
                        responseLength: recaptchaResponse ? recaptchaResponse.value.length : 0,
                        iframeCount: recaptchaIframes.length,
                        sitekey: recaptchaDiv ? recaptchaDiv.getAttribute('data-sitekey') : null
                    };

                    // 2. ПОИСК ВСЕХ КНОПОК, СВЯЗАННЫХ С ФОРМОЙ CPA
                    const cpaForm = document.getElementById('FindACPABottinForm');
                    if (cpaForm) {
                        result.form = {
                            exists: true,
                            action: cpaForm.action,
                            method: cpaForm.method,
                            buttonsInside: cpaForm.querySelectorAll('button, input[type="submit"], input[type="button"]').length
                        };

                        // Ищем кнопки ВНЕ формы, но связанные с ней
                        const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
                        allButtons.forEach((btn, index) => {
                            const isVisible = btn.offsetParent !== null;
                            const isEnabled = !btn.disabled;
                            const formAttr = btn.getAttribute('form');
                            const isInCpaForm = cpaForm.contains(btn);
                            const onClick = btn.getAttribute('onclick');
                            const text = btn.textContent || btn.value || 'NO_TEXT';

                            // Проверяем, связана ли кнопка с формой CPA (СТРОГАЯ ПРОВЕРКА)
                            const isRelatedToCpaForm = isInCpaForm ||
                                                    formAttr === 'FindACPABottinForm' ||
                                                    (onClick && onClick.includes('FindACPA'));

                            if (isRelatedToCpaForm || (isVisible && isEnabled && text.toLowerCase().includes('search'))) {
                                result.buttons.push({
                                    index: index,
                                    text: text,
                                    type: btn.type,
                                    id: btn.id || 'NO_ID',
                                    className: btn.className,
                                    visible: isVisible,
                                    enabled: isEnabled,
                                    inCpaForm: isInCpaForm,
                                    formAttr: formAttr,
                                    onClick: onClick,
                                    position: {
                                        left: Math.round(btn.getBoundingClientRect().left),
                                        top: Math.round(btn.getBoundingClientRect().top)
                                    }
                                });
                            }
                        });
                    }

                    return result;
                }
            """)

            logger.info("=== АНАЛИЗ КАПЧИ ===")
            captcha_info = captcha_and_button_analysis['captcha']
            logger.info(f"Div капчи: {captcha_info['divExists']}")
            logger.info(f"Поле ответа: {captcha_info['responseExists']}")
            logger.info(f"Длина токена: {captcha_info['responseLength']}")
            logger.info(f"Количество iframe: {captcha_info['iframeCount']}")
            logger.info(f"Sitekey: {captcha_info['sitekey'][:10] if captcha_info['sitekey'] else 'None'}...")

            logger.info("=== АНАЛИЗ КНОПОК ===")
            buttons_info = captcha_and_button_analysis['buttons']
            logger.info(f"Найдено связанных кнопок: {len(buttons_info)}")

            for btn in buttons_info:
                logger.info(f"Кнопка: '{btn['text']}' | id={btn['id']} | "
                           f"visible={btn['visible']} | enabled={btn['enabled']} | "
                           f"inForm={btn['inCpaForm']} | formAttr={btn['formAttr']} | "
                           f"pos=({btn['position']['left']}, {btn['position']['top']})")

            # ПРОВЕРЯЕМ СОСТОЯНИЕ КАПЧИ
            if captcha_info['responseLength'] < 100:
                logger.error("❌ Токен капчи слишком короткий или отсутствует!")
                return

            # ФИНАЛЬНАЯ ПРОВЕРКА И ИСПРАВЛЕНИЕ ГАЛОЧЕК ПЕРЕД ОТПРАВКОЙ
            logger.info("🔧 ФИНАЛЬНАЯ ПРОВЕРКА ГАЛОЧЕК ПЕРЕД ОТПРАВКОЙ...")

            final_fix_result = await page.evaluate("""
                () => {
                    const expectedIds = ['ListeClienteleDesserviesLeftColumn_0__Selected', 'acceptClients'];
                    const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
                    let fixedCount = 0;

                    console.log('🔧 ФИНАЛЬНОЕ ИСПРАВЛЕНИЕ ГАЛОЧЕК...');

                    allCheckboxes.forEach(checkbox => {
                        if (expectedIds.includes(checkbox.id)) {
                            // Эти должны быть отмечены
                            if (!checkbox.checked) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('✅ ИСПРАВЛЕНО: Отмечена нужная галочка:', checkbox.id);
                                fixedCount++;
                            }
                        } else {
                            // Все остальные должны быть сняты
                            if (checkbox.checked) {
                                checkbox.checked = false;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('❌ ИСПРАВЛЕНО: Снята лишняя галочка:', checkbox.id);
                                fixedCount++;
                            }
                        }
                    });

                    // Проверяем финальное состояние
                    const checkedBoxes = Array.from(allCheckboxes).filter(cb => cb.checked);

                    return {
                        fixedCount: fixedCount,
                        totalCheckboxes: allCheckboxes.length,
                        checkedCheckboxes: checkedBoxes.length,
                        checkedDetails: checkedBoxes.map(cb => ({
                            id: cb.id,
                            name: cb.name,
                            value: cb.value,
                            label: 'No label'
                        }))
                    };
                }
            """)

            logger.info(f"🔧 Исправлено {final_fix_result['fixedCount']} галочек")
            logger.info(f"📊 ФИНАЛЬНОЕ состояние формы перед отправкой: {final_fix_result['checkedCheckboxes']} из {final_fix_result['totalCheckboxes']} галочек выбрано")

            # Проверяем, что выбраны ТОЛЬКО правильные галочки
            expected_checked = ['ListeClienteleDesserviesLeftColumn_0__Selected', 'acceptClients']
            actual_checked = [cb['id'] for cb in final_fix_result['checkedDetails']]

            if set(actual_checked) == set(expected_checked):
                logger.info("🎯 ✅ ОТЛИЧНО! Перед отправкой выбраны ТОЛЬКО правильные галочки!")
            else:
                logger.error(f"❌ КРИТИЧЕСКАЯ ОШИБКА! Ожидались: {expected_checked}, но выбраны: {actual_checked}")
                logger.error("Отправка формы может не сработать из-за неправильных галочек!")

            logger.info(f"Детали выбранных галочек: {final_fix_result['checkedDetails']}")

            # ИГНОРИРУЕМ КНОПКИ! ОТПРАВЛЯЕМ ФОРМУ CPA НАПРЯМУЮ!
            logger.info("🚀 ОТПРАВЛЯЕМ ФОРМУ CPA НАПРЯМУЮ (БЕЗ ПОИСКА КНОПОК)...")

            form_submit_result = await page.evaluate("""
                () => {
                    const cpaForm = document.getElementById('FindACPABottinForm');
                    if (!cpaForm) {
                        return { success: false, reason: 'Форма CPA не найдена' };
                    }

                    console.log('✅ Найдена форма CPA:', cpaForm.id, cpaForm.action);

                    // Убеждаемся, что токен капчи включен в форму
                    const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                    if (recaptchaResponse && recaptchaResponse.value.length > 100) {
                        console.log('🔐 Токен капчи найден, длина:', recaptchaResponse.value.length);

                        // Создаем скрытое поле в форме для токена (если его еще нет)
                        let hiddenToken = cpaForm.querySelector('input[name="g-recaptcha-response"]');
                        if (!hiddenToken) {
                            hiddenToken = document.createElement('input');
                            hiddenToken.type = 'hidden';
                            hiddenToken.name = 'g-recaptcha-response';
                            cpaForm.appendChild(hiddenToken);
                            console.log('➕ Создано скрытое поле для токена');
                        }
                        hiddenToken.value = recaptchaResponse.value;
                        console.log('✅ Токен добавлен в форму');
                    } else {
                        console.error('❌ Токен капчи не найден или слишком короткий!');
                        return { success: false, reason: 'Токен капчи недоступен' };
                    }

                    // Проверяем все поля формы перед отправкой
                    const formData = new FormData(cpaForm);
                    console.log('📝 Данные формы перед отправкой:');
                    for (let [key, value] of formData.entries()) {
                        console.log(`  ${key}: ${value}`);
                    }

                    // КРИТИЧЕСКИ ВАЖНО: Проверяем, включен ли токен капчи в данные формы
                    const hasRecaptchaToken = Array.from(formData.entries()).some(([key, value]) =>
                        key === 'g-recaptcha-response' && value && value.length > 100
                    );
                    console.log('🔐 Токен капчи в данных формы:', hasRecaptchaToken);

                    if (!hasRecaptchaToken) {
                        console.log('⚠️ ТОКЕН КАПЧИ НЕ НАЙДЕН В ДАННЫХ ФОРМЫ! Добавляем принудительно...');
                        const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                        if (recaptchaResponse && recaptchaResponse.value) {
                            // Создаем скрытое поле в форме для токена
                            const hiddenTokenField = document.createElement('input');
                            hiddenTokenField.type = 'hidden';
                            hiddenTokenField.name = 'g-recaptcha-response';
                            hiddenTokenField.value = recaptchaResponse.value;
                            cpaForm.appendChild(hiddenTokenField);
                            console.log('✅ Токен капчи добавлен в форму как скрытое поле');
                        }
                    }

                    // ПРОБУЕМ AJAX ОТПРАВКУ ВМЕСТО ОБЫЧНОЙ ОТПРАВКИ ФОРМЫ
                    try {
                        const formData = new FormData(cpaForm);

                        // Убеждаемся, что токен капчи включен
                        const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                        if (recaptchaResponse && recaptchaResponse.value) {
                            formData.set('g-recaptcha-response', recaptchaResponse.value);
                            console.log('✅ Токен капчи принудительно добавлен в FormData');
                        }

                        console.log('🚀 Отправляем форму через AJAX...');

                        // Отправляем через fetch
                        fetch(cpaForm.action, {
                            method: 'POST',
                            body: formData
                        }).then(response => {
                            console.log('📡 AJAX ответ получен:', response.status, response.statusText);
                            if (response.ok) {
                                console.log('✅ AJAX запрос успешен, перенаправляем...');
                                window.location.href = response.url;
                            } else {
                                console.error('❌ AJAX запрос неуспешен:', response.status);
                            }
                        }).catch(error => {
                            console.error('❌ Ошибка AJAX запроса:', error);
                            // Fallback к обычной отправке формы
                            console.log('🔄 Fallback к обычной отправке формы...');
                            cpaForm.submit();
                        });

                        return { success: true, method: 'ajax_submit' };
                    } catch (e) {
                        console.error('❌ Ошибка при AJAX отправке, пробуем обычную отправку:', e);
                        try {
                            cpaForm.submit();
                            console.log('🚀 Форма CPA отправлена через submit()!');
                            return { success: true, method: 'direct_form_submit_fallback' };
                        } catch (e2) {
                            console.error('❌ Ошибка при обычной отправке формы:', e2);
                            return { success: false, reason: e2.message };
                        }
                    }
                }
            """)

            if form_submit_result['success']:
                logger.info(f"✅ Форма CPA отправлена успешно методом: {form_submit_result['method']}")
            else:
                logger.error(f"❌ Не удалось отправить форму CPA: {form_submit_result['reason']}")
                return



            # Ждем навигации к ПРАВИЛЬНОМУ URL (форма CPA, а не общий поиск)
            logger.info("⏳ Ожидаем навигацию к результатам CPA...")
            try:
                # Ждем изменения URL на правильный (FindACPABottinFormSubmit)
                await page.wait_for_url("**/FindACPABottinFormSubmit**", timeout=30000)
                logger.info("✅ Навигация к результатам CPA завершена успешно!")
            except Exception as nav_error:
                logger.warning(f"⚠️ Навигация не произошла или завершилась с ошибкой: {nav_error}")
                # Ждем немного и проверяем, изменился ли URL
                await page.wait_for_timeout(5000)
                current_url = page.url

                if "FindACPABottinFormSubmit" in current_url:
                    logger.info(f"✅ URL изменился на правильный CPA URL: {current_url}")
                elif "search" in current_url.lower() and "k=" in current_url:
                    logger.error(f"❌ ПОПАЛИ НА ОБЩИЙ ПОИСК САЙТА: {current_url}")
                    logger.error("Это означает, что форма CPA не была отправлена правильно!")
                    return
                elif current_url != initial_url:
                    logger.info(f"✅ URL изменился: {current_url}, анализируем...")
                else:
                    logger.warning("❌ URL не изменился, возможно навигация не произошла")

            logger.info("⏳ Ожидаем результаты...")

            # Ждем загрузки
            await page.wait_for_load_state("domcontentloaded")
            await page.wait_for_timeout(5000)

            # Анализируем результаты
            current_url = page.url
            logger.info(f"URL после поиска: {current_url}")

            # Получаем информацию о результатах
            results_info = await page.evaluate("""
                () => {
                    const pageText = document.body.innerText;
                    const url = window.location.href;

                    // Проверяем, находимся ли мы на странице результатов или снова на форме
                    const isFormPage = pageText.includes('Search criteria') ||
                                     pageText.includes('Last name of CPA') ||
                                     pageText.includes('First name of CPA') ||
                                     url.includes('/find-a-cpa/cpa-directory/');

                    const isResultsPage = url.includes('/search/') ||
                                        url.includes('/results/') ||
                                        url.includes('FindACPABottinFormSubmit');

                    // Ищем текст о количестве результатов
                    const resultMatches = pageText.match(/(\\d+)\\s*-\\s*(\\d+)\\s*of\\s*(\\d+)\\s*result/i);

                    // Ищем карточки CPA различными способами (обновленные селекторы)
                    const possibleSelectors = [
                        '.cpa-card', '.member-card', '.profile-card', '.directory-entry',
                        '.search-result', '.result-item', '.cpa-listing', '.member-listing',
                        '[data-cpa]', '[data-member]', '.contact-card', '.professional-card',
                        '.cpa-profile', '.professional-profile', '.directory-card',
                        '.member-info', '.cpa-info', '.professional-info',
                        'article.cpa-entry', 'div.profile-summary', 'li.item',
                        '.search-result-item', '.result-card'
                    ];

                    let foundCards = 0;
                    let cardSelector = null;

                    for (const selector of possibleSelectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            foundCards = elements.length;
                            cardSelector = selector;
                            break;
                        }
                    }

                    // Ищем любые элементы с email или телефоном
                    const emailElements = document.querySelectorAll('a[href^="mailto"]');
                    const phoneElements = document.querySelectorAll('a[href^="tel"]');

                    // Ищем элементы, содержащие имена (возможные CPA)
                    const nameElements = document.querySelectorAll('h1, h2, h3, h4, .name, .title, .professional-name');
                    let possibleNames = [];

                    nameElements.forEach(el => {
                        const text = el.textContent.trim();
                        // Простая проверка на имя (содержит буквы и возможно запятую)
                        if (text.length > 3 && text.length < 100 && /[A-Za-z]/.test(text)) {
                            possibleNames.push(text);
                        }
                    });

                    return {
                        url: url,
                        isFormPage: isFormPage,
                        isResultsPage: isResultsPage,
                        pageText: pageText.substring(0, 1500),
                        hasResultsText: pageText.toLowerCase().includes('result'),
                        resultMatches: resultMatches,
                        emailCount: emailElements.length,
                        phoneCount: phoneElements.length,
                        foundCards: foundCards,
                        cardSelector: cardSelector,
                        possibleNames: possibleNames.slice(0, 10), // Первые 10 имен
                        pageTitle: document.title
                    };
                }
            """)

            logger.info(f"Анализ результатов: {results_info}")

            # Проверяем, на какой странице мы находимся
            if results_info['isFormPage']:
                logger.error("❌ МЫ СНОВА НА СТРАНИЦЕ ФОРМЫ ПОИСКА!")
                logger.error("Это означает, что форма не была правильно отправлена или сервер вернул ошибку")
                logger.info("Возможные причины:")
                logger.info("1. Не все обязательные поля заполнены")
                logger.info("2. Капча не была полностью решена")
                logger.info("3. Сервер отклонил запрос")
                logger.info("4. Нужны дополнительные параметры формы")
            elif results_info['isResultsPage']:
                logger.info("✅ МЫ НА СТРАНИЦЕ РЕЗУЛЬТАТОВ!")

                if results_info['resultMatches']:
                    logger.info(f"📊 Найдены результаты: {results_info['resultMatches'][0]}")
                    logger.info(f"📧 Email ссылок: {results_info['emailCount']}")
                    logger.info(f"📞 Телефонных ссылок: {results_info['phoneCount']}")

                    if results_info['foundCards'] > 0:
                        logger.info(f"🎯 Найдено {results_info['foundCards']} карточек CPA с селектором: {results_info['cardSelector']}")

                    if results_info['possibleNames']:
                        logger.info(f"👥 Возможные имена CPA: {results_info['possibleNames'][:5]}")

                elif results_info['emailCount'] > 0 or results_info['phoneCount'] > 0:
                    logger.info(f"📧 Найдены контакты без явного счетчика результатов:")
                    logger.info(f"   Email ссылок: {results_info['emailCount']}")
                    logger.info(f"   Телефонных ссылок: {results_info['phoneCount']}")

                    if results_info['possibleNames']:
                        logger.info(f"👥 Возможные имена: {results_info['possibleNames'][:5]}")
                else:
                    logger.warning("⚠️ Страница результатов найдена, но данные CPA не обнаружены")
                    logger.info("Возможно, поиск не дал результатов для выбранной категории")
            else:
                logger.warning("❓ НЕИЗВЕСТНЫЙ ТИП СТРАНИЦЫ")
                logger.info(f"URL: {results_info['url']}")
                logger.info(f"Заголовок: {results_info['pageTitle']}")

            # Показываем часть текста страницы для диагностики
            logger.info("📄 Текст страницы (первые 1500 символов):")
            logger.info(results_info['pageText'])

            # Ждем для анализа
            input("\nПроанализируйте результаты в браузере и нажмите Enter...")

        except Exception as e:
            logger.error(f"Ошибка: {e}")
        finally:
            if anticaptcha_session and not anticaptcha_session.closed:
                await anticaptcha_session.close()
            if twocaptcha_session and not twocaptcha_session.closed:
                await twocaptcha_session.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(run_test_parser())