const XLSX = require('xlsx');
const fs = require('fs').promises;
const path = require('path');

async function saveResultsToExcel(results, outputDir = './output', filename = null) {
    if (!results || results.length === 0) {
        console.log('⚠️ Нет результатов для сохранения в Excel');
        return;
    }

    // Создаем папку output если её нет
    try {
        await fs.mkdir(outputDir, { recursive: true });
    } catch (e) {
        // Папка уже существует
    }

    if (!filename) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        filename = `cpa_results_${timestamp}.xlsx`;
    }

    const filepath = path.join(outputDir, filename);

    try {
        // Подготавливаем данные для Excel
        const excelData = results.map((result, index) => ({
            '№': index + 1,
            'Имя': result.name || 'Не указано',
            'Email': result.email || 'Не указан',
            'Телефон': result.phone || 'Не указан',
            'Ссылка на профиль': result.profileUrl || 'Не указана',
            'Категории': Array.isArray(result.categories) ? result.categories.join(', ') : (result.categories || 'Не указаны'),
            'Страница': result.pageNumber || 1,
            'Дополнительная информация': result.text ? result.text.substring(0, 200) + (result.text.length > 200 ? '...' : '') : 'Нет данных',
            'Дата парсинга': new Date().toLocaleString('ru-RU')
        }));

        // Создаем рабочую книгу
        const workbook = XLSX.utils.book_new();

        // Создаем лист с данными
        const worksheet = XLSX.utils.json_to_sheet(excelData);

        // Настраиваем ширину колонок
        const columnWidths = [
            { wch: 5 },   // №
            { wch: 25 },  // Имя
            { wch: 30 },  // Email
            { wch: 20 },  // Телефон
            { wch: 40 },  // Ссылка на профиль
            { wch: 25 },  // Категории
            { wch: 10 },  // Страница
            { wch: 50 },  // Дополнительная информация
            { wch: 20 }   // Дата парсинга
        ];
        worksheet['!cols'] = columnWidths;

        // Добавляем лист в книгу
        XLSX.utils.book_append_sheet(workbook, worksheet, 'CPA Results');

        // Создаем лист со статистикой
        const stats = {
            'Общее количество записей': results.length,
            'Записи с email': results.filter(r => r.email).length,
            'Записи с телефоном': results.filter(r => r.phone).length,
            'Записи с профилем': results.filter(r => r.profileUrl).length,
            'Дата создания отчета': new Date().toLocaleString('ru-RU')
        };

        const statsData = Object.entries(stats).map(([key, value]) => ({
            'Параметр': key,
            'Значение': value
        }));

        const statsWorksheet = XLSX.utils.json_to_sheet(statsData);
        statsWorksheet['!cols'] = [{ wch: 30 }, { wch: 20 }];
        XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'Статистика');

        // Создаем лист с категориями
        const categoriesStats = {};
        results.forEach(result => {
            if (result.categories) {
                const cats = Array.isArray(result.categories) ? result.categories : [result.categories];
                cats.forEach(cat => {
                    categoriesStats[cat] = (categoriesStats[cat] || 0) + 1;
                });
            }
        });

        const categoriesData = Object.entries(categoriesStats).map(([category, count]) => ({
            'Категория': category,
            'Количество записей': count,
            'Процент от общего': ((count / results.length) * 100).toFixed(1) + '%'
        }));

        if (categoriesData.length > 0) {
            const categoriesWorksheet = XLSX.utils.json_to_sheet(categoriesData);
            categoriesWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }, { wch: 20 }];
            XLSX.utils.book_append_sheet(workbook, categoriesWorksheet, 'По категориям');
        }

        // Сохраняем файл
        XLSX.writeFile(workbook, filepath);

        console.log(`📊 Результаты сохранены в Excel: ${filepath}`);
        console.log(`📈 Всего записей: ${results.length}`);
        console.log(`📧 С email: ${results.filter(r => r.email).length}`);
        console.log(`📞 С телефоном: ${results.filter(r => r.phone).length}`);
        console.log(`🔗 С профилем: ${results.filter(r => r.profileUrl).length}`);

        return filepath;

    } catch (error) {
        console.error(`❌ Ошибка при сохранении Excel файла: ${error.message}`);
        console.log('💡 Убедитесь, что установлена библиотека xlsx: npm install xlsx');
        throw error;
    }
}

module.exports = { saveResultsToExcel };